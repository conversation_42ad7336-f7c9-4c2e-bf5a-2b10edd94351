{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["这是第二版, 有几个缺陷\n", "- K线数据不够, 没加主买量, ohl\n", "- 没加transformer\n", "- 训练数据不够多(下多点分钟级数据)\n", "\n", "改进了\n", "- 没用残差网络\n", "- 卷积层级数不够\n", "- MLP层级数不够"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "import ccxt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["服务器时间: 1741409501282\n"]}], "source": ["# Step 1: Fetch data\n", "exchange = ccxt.okx({\n", "    'httpsProxy': 'http://172.22.48.1:7890',  # 设置代理\n", "    'enableRateLimit': True,  # 启用速率限制\n", "})\n", "symbol = 'SOL/USDT'  # 交易对\n", "timeframe = '1d'     # 时间框架（1天）\n", "since = exchange.parse8601('2023-03-04T00:00:00Z')  # 开始时间（ISO 8601格式）\n", "limit = 365          # 限制为365条数据（1年）\n", "\n", "# 获取 Binance 服务器时间\n", "server_time = exchange.fetch_time()\n", "print(f\"服务器时间: {server_time}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 下载数据\n", "ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since, limit)\n", "\n", "# 转换为DataFrame\n", "df = pd.DataFrame(ohlcv, columns=['date', 'open', 'high', 'low', 'close', 'volume'])\n", "# 将时间戳转换为日期格式\n", "df['date'] = pd.to_datetime(df['date'], unit='ms')\n", "data = pd.DataFrame({\n", "\t'Date': df['date'],\n", "    'Price': df['close'],\n", "    'Volume': df['volume']\n", "})"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 计算收益率\n", "data['Return'] = np.log(data['Price'] / data['Price'].shift(1))\n", "# 计算波动率\n", "rolling_window = 10\n", "data['Volatility'] = data['Return'].rolling(window=rolling_window).std()\n", "# 计算成交量 TODO: 以后再加个主买量\n", "data['LogVolume'] = np.log(data['Volume'] + 1)\n", "data = data.dropna()\n", "# 准备 特征和标签 TODO: 建议再加上open, high, low\n", "features = data[['Return', 'Volatility', 'LogVolume']].values\n", "labels = data['Return'].shift(-rolling_window).dropna().values  # 预测10天后的收益率"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 确保 特征 和 标签 的长度相同\n", "features = features[:-rolling_window]\n", "# 对特征进行标准化\n", "scaler = StandardScaler()\n", "features = scaler.fit_transform(features)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["print(features.shape[-1])"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征\n", "sequence_length = 30\n", "X, y = [], []\n", "for i in range(len(features) - sequence_length):\n", "    X.append(features[i:i + sequence_length])\n", "    y.append(labels[i + sequence_length - 1])  # 取训练数据最后一个时间步的标签作为预测目标\n", "X, y = np.array(X), np.array(y)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 将数据划分为 训练集 和 测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0\n", "1\n", "2\n"]}], "source": ["from torch import nn\n", "from torch.nn.utils.parametrizations import weight_norm\n", "import torch\n", "\n", "class Chomp1d(nn.<PERSON><PERSON><PERSON>):  \n", "\t# 裁剪多余填充的未来数据以保持因果性\n", "\tdef __init__(self, chomp_size):\n", "\t\tsuper().__init__()\n", "\t\tself.chomp_size = chomp_size\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\treturn x[:, :, :-self.chomp_size].contiguous()\n", "\t\n", "\n", "class TemporalBlock(nn.Module):\n", "\t\n", "\tdef __init__(self, n_inputs, n_outputs, kernel_size, dilation):\n", "\t\t'''\n", "\t\t- n_inputs: 输入通道数\n", "\t\t- n_outputs: 输出通道数\n", "\t\t- kernel_size: 卷积核大小\n", "\t\t- dilation: 膨胀系数\n", "\t\t'''\n", "\t\tsuper().__init__()\n", "\t\tpadding = (kernel_size-1) * dilation  # 因果卷积的填充计算\n", "\t\tself.conv = nn.Sequential(\n", "\t\t\t# nn.Conv1d的padding是左右各填充padding个\n", "\t\t\tweight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),\n", "\t\t\tChomp1d(padding),\n", "\t\t\tnn.ReLU(),\n", "\t\t\tnn.Dropout(0.2)\n", "\t\t)\n", "\t\tself.conv2 = nn.Sequential(\n", "\t\t\tweight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),\n", "\t\t\tChomp1d(padding),\n", "\t\t\tnn.ReLU(),\n", "\t\t\tnn.Dropout(0.2)\n", "\t\t)\n", "\t\tself.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)\n", "\t\tout = self.conv(x)\n", "\t\tout = self.conv2(x)\n", "\t\tres = x if self.downsample is None else self.downsample(x)\n", "\t\treturn nn.ReLU()(out + res)  # 残差连接\n", "\t\n", "\n", "class TCN(nn.Module):\n", "\t'''\n", "\t- input_dim: 输入通道数\n", "\t- num_channels: 共几层, 每层的输出通道数\n", "\t- kernel_size: 卷积核大小\n", "\t- output_size: 输出通道数\n", "\t'''\n", "\tdef __init__(self, input_dim, num_channels, kernel_size=3, output_size=1):\n", "\t\tsuper().__init__()\n", "\t\tlayers = []\n", "\t\tnum_levels = len(num_channels)\n", "\t\tfor i in range(num_levels):\n", "\t\t\tdilation = 2 ** i  # 指数级膨胀系数\n", "\t\t\tprint(i)\n", "\t\t\tlayers += [TemporalBlock(input_dim if i==0 else num_channels[i-1], \n", "\t\t\t\t\t\t\t\t\tnum_channels[i], kernel_size, dilation)]\n", "\t\tself.network = nn.Sequential(*layers)\n", "\t\tself.linear = nn.Linear(num_channels[-1], output_size)\n", "\t\t\n", "\tdef forward(self, x):\n", "\t\t# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)\n", "\t\tx = x.permute(0, 2, 1)\n", "\t\tx = self.network(x)\n", "\t\tx = x[:, :, -1]  # 取序列的最后一个时间步 (200,32,30) -> (200,32)\n", "\t\tx = self.linear(x)  # 将输出传递给线性层 (200,32) -> (200,1)\n", "\t\treturn x.squeeze()  # (200,1) -> (200)\n", "\t\n", "tcn = TCN(features.shape[-1], (32, 64, 32))  # 实例化模型\n", "optimizer = torch.optim.Adam(tcn.parameters())\n", "loss_fn = nn.MSELoss()\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练到第0次, loss:0.04720931500196457\n", "训练到第100次, loss:0.0014328925171867013\n", "训练到第200次, loss:0.0008832392632029951\n", "训练到第300次, loss:0.000565770547837019\n", "训练到第400次, loss:0.0005186240887269378\n", "训练到第500次, loss:0.000318073172820732\n", "训练到第600次, loss:0.0002740878553595394\n", "训练到第700次, loss:0.00023597570543643087\n", "训练到第800次, loss:0.0002381276135565713\n", "训练到第900次, loss:0.00026137218810617924\n", "训练完成\n"]}], "source": ["# 训练模型\n", "epochs = 1000\n", "x = torch.from_numpy(X_train).float()\n", "y = torch.from_numpy(y_train).float()\n", "for epoch in range(epochs):\n", "\t# 前向传播\n", "\ty_pred = tcn(x)\n", "\t# 计算loss\n", "\tloss = loss_fn(y_pred, y)\n", "\tif epoch % 100 == 0:\n", "\t\tprint(f'训练到第{epoch}次, loss:{loss}')\n", "\t# 梯度清零\n", "\toptimizer.zero_grad()\n", "\t# 反向传播\n", "\tloss.backward()\n", "\t# 更新参数\n", "\toptimizer.step()\n", "print('训练完成')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'torch.Tensor'>\n", "<class 'torch.Tensor'>\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 预测\n", "x_text_ = torch.from_numpy(X_test).float()\n", "y_test_ = torch.from_numpy(y_test).float()\n", "y_pred_ = tcn(x_text_)\n", "# 转为numpy数组\n", "print(type(y_test_))\n", "print(type(y_pred_))\n", "y_test_np = y_test_.detach().numpy()\n", "y_pred_np = y_pred_.detach().numpy()\n", "# 画图对比\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(y_test_np, label='Actual Returns')\n", "plt.plot(y_pred_np, label='Predicted Returns')\n", "plt.title('Comparison of Actual vs Predicted Returns')\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}