# StkpModels

机器学习模型预测股价

- main.py 用现有模型实时预测
- backtest.py 用现有模型回测

## 优化方案

0. 特征工程用其它的因子, 去掉预测不准的因子
1. 换GRU, mamba模型
2. fvg滑动窗口判断
4. 滚动训练预测
5. 用回归模型 预测未来10根k线的平均收益率
6. 窗口改为400
7. 直接用过去200不太对啊, 因为可能会跨天了
8. LightGBM

平衡XGBoost测试集结果:
  总体准确率: 36.06%
预测类别分布 (0:跌, 1:平, 2:涨): [ 981 1190  935]
真实类别分布 (0:跌, 1:平, 2:涨): [ 987 1137  982]

各类别准确率:
  类别0 (跌): 33.43% (987个样本)
  类别1 (平): 44.33% (1137个样本)
  类别2 (涨): 29.12% (982个样本)
```
import xgboost as xgb
model = xgb.XGBClassifier()
model.fit(X_train, y_train)

importances = model.feature_importances_
feature_names = X_train.columns
importance_df = pd.DataFrame({'feature': feature_names, 'importance': importances})
importance_df.sort_values(by='importance', ascending=False, inplace=True)
print(importance_df)
```

## 实验对比表格

### 模型性能对比表

| 标的代码  | 模型类型     | 序列长度   | 特征维度  | 训练准确率   | 验证准确率   | 测试准确率  | 备注      |
| -------- | ----------- | -------- | -------- | ---------- | ---------- | ---------- | --------- |
| TQQQ.US  | Transformer | 60       | 14       | 85.2%      | 82.1%      | 79.8%      | 基线模型  |
| TQQQ.US  | LSTM        | 60       | 14       | 83.1%      | 80.5%      | 78.2%      | 对比 LSTM |
| TQQQ.US  | TCN         | 60       | 14       | 84.7%      | 81.8%      | 79.1%      | 对比 TCN  |
| TQQQ.US  | GRU         | 60       | 14       | 84.7%      | 81.8%      | 79.1%      | 对比 GRU  |
| TQQQ.US  | Transformer | 200      | 14       | -          | -          | -         | 长序列    |
| TQQQ.US  | Transformer | 30       | 14       | -          | -          | -          | 短序列    |

Lr=0.001, 梯度为1时
============================================================
测试集结果:
  交叉熵损失: 1.064516
  分类准确率: 49.45%
  预测置信度范围: 0.3345 - 0.6883
预测类别分布 (0:跌, 1:平, 2:涨): [ 348 1649 1109]
真实类别分布 (0:跌, 1:平, 2:涨): [ 635 1785  686]
============================================================

Lr=0.001, 梯度为5时
============================================================
测试集结果:
  交叉熵损失: 1.057849
  分类准确率: 51.84%
  预测置信度范围: 0.3341 - 0.6385
预测类别分布 (0:跌, 1:平, 2:涨): [ 341 1835  930]
真实类别分布 (0:跌, 1:平, 2:涨): [ 635 1785  686]
============================================================

Lr=0.004, 梯度为5时
============================================================
测试集结果:
  交叉熵损失: 1.091135
  分类准确率: 52.12%
  预测置信度范围: 0.3345 - 0.7527
预测类别分布 (0:跌, 1:平, 2:涨): [ 743 2311   52]
真实类别分布 (0:跌, 1:平, 2:涨): [ 635 1785  686]
============================================================

Lr=0.01, 梯度为5时
============================================================
测试集结果:
  交叉熵损失: 1.085536
  分类准确率: 51.03%
  预测置信度范围: 0.3491 - 0.6138
预测类别分布 (0:跌, 1:平, 2:涨): [1166 1940    0]
真实类别分布 (0:跌, 1:平, 2:涨): [ 635 1785  686]
============================================================


seq_len=60, Lr=0.001, 梯度为5时
============================================================
测试集结果:
  交叉熵损失: 1.057849
  分类准确率: 51.84%
  预测置信度范围: 0.3341 - 0.6385
预测类别分布 (0:跌, 1:平, 2:涨): [ 341 1835  930]
真实类别分布 (0:跌, 1:平, 2:涨): [ 635 1785  686]
============================================================

seq_len=200, Lr=0.001, 梯度为5时
============================================================
测试集结果:
  交叉熵损失: 1.057055
  分类准确率: 52.32%
  预测置信度范围: 0.3345 - 0.6525
预测类别分布 (0:跌, 1:平, 2:涨): [ 271 1905  909]
真实类别分布 (0:跌, 1:平, 2:涨): [ 629 1772  684]
============================================================
如果在其基础上再训练
============================================================
测试集结果:
  交叉熵损失: 1.058554
  分类准确率: 53.94%
  预测置信度范围: 0.3351 - 0.6984
预测类别分布 (0:跌, 1:平, 2:涨): [ 185 2015  885]
真实类别分布 (0:跌, 1:平, 2:涨): [ 629 1772  684]
============================================================

### 超参数对比表

| 学习率 | Batch Size | 隐藏层数 | 注意力头数 | Dropout | 损失函数     | 最佳 Epoch | 训练时间 |
| ------ | ---------- | -------- | ---------- | ------- | ------------ | ---------- | -------- |
| 0.001  | 32         | 2        | 4          | 0.1     | CrossEntropy | 45         | 2.5h     |
| 0.0005 | 32         | 2        | 4          | 0.1     | CrossEntropy | -          | -        |
| 0.002  | 32         | 2        | 4          | 0.1     | CrossEntropy | -          | -        |
| 0.001  | 16         | 2        | 4          | 0.1     | CrossEntropy | -          | -        |
| 0.001  | 64         | 2        | 4          | 0.1     | CrossEntropy | -          | -        |

### 特征工程对比表

| 基础特征 | 技术指标           | 时间特征  | 市场情绪     | 特征总数 | 测试准确率 | 改进幅度 |
| -------- | ------------------ | --------- | ------------ | -------- | ---------- | -------- |
| OHLCV    | MA,RSI,波动率      | 无        | 无           | 14       | 79.8%      | 基线     |
| OHLCV    | MA,RSI,波动率      | 小时,星期 | 无           | 16       | -          | -        |
| OHLCV    | MA,RSI,波动率,MACD | 无        | 无           | 17       | -          | -        |
| OHLCV    | MA,RSI,波动率      | 无        | VIX,成交量比 | 16       | -          | -        |

### 数据预处理对比表

| 标准化方法 | 窗口大小 | 数据分割 | 样本平衡 | 测试准确率 | 备注       |
| ---------- | -------- | -------- | -------- | ---------- | ---------- |
| 滚动标准化 | 200      | 7:2:1    | 无       | 79.8%      | 基线       |
| Z-Score    | 200      | 7:2:1    | 无       | -          | 标准化对比 |
| MinMax     | 200      | 7:2:1    | 无       | -          | 标准化对比 |
| 滚动标准化 | 100      | 7:2:1    | 无       | -          | 窗口对比   |
| 滚动标准化 | 200      | 7:2:1    | SMOTE    | -          | 样本平衡   |

### 实验记录模板

```
实验ID: E0XX
日期: YYYY-MM-DD
目标: 验证XXX对模型性能的影响
控制变量: 除XXX外，其他参数保持与E001一致
变化参数: XXX = YYY
结果:
- 训练准确率: XX%
- 验证准确率: XX%
- 测试准确率: XX%
结论: XXX
下一步: XXX
```
