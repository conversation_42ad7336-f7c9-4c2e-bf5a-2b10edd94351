'''

'''

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
from torch import nn
from torch.utils.data import TensorDataset, DataLoader

class LSTMPredictor(nn.Module):
	def __init__(self, sequence_dim, hidden_dim, drop_out_prob = 0.2):
		super(LSTMPredictor, self).__init__()
		self.sequence_dim = sequence_dim
		self.hidden_dim = hidden_dim
		self.lstm = nn.LSTM(sequence_dim, hidden_dim)
		self.dropout = nn.Dropout(p=drop_out_prob)
		self.lstm2 = nn.LSTM(hidden_dim, hidden_dim)
		self.dropout = nn.Dropout(p=drop_out_prob)
		self.lstm3 = nn.LSTM(hidden_dim, hidden_dim)
		self.dropout = nn.Dropout(p=drop_out_prob)
		self.hidden2target = nn.Linear(hidden_dim, 1)

	def forward(self, X):
		lstm_out, _ = self.lstm(X)
		drop_out1 = self.dropout(lstm_out)

		lstm_out2, _ = self.lstm2(drop_out1)
		drop_out2 = self.dropout(lstm_out2)

		lstm_out3, _ = self.lstm3(drop_out2)
		drop_out3 = self.dropout(lstm_out3)

		# 取最后一个时间步的输出 
		last_step_output = drop_out3[:, -1, :]  # 形状: (batch_size, hidden_dim)
		pred_out = self.hidden2target(last_step_output)  # 形状: (batch_size, 1)
		return pred_out.squeeze()  # 移除最后一维 -> (batch_size)
	
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 读取数据
df = pd.read_csv('./small_data/BTC_USDT.csv')

# 计算收益率
data = pd.DataFrame()
data['Return'] = np.log(df['close'] / df['close'].shift(1))
# 计算成交量
data['LogVolume'] = np.log(df['volume'] + 1)
# 计算买卖方实力
data['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5
# 计算IBS
data['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)


# 预计几个tick后的收益率
AFTER_TICK_NUM = 1
# 准备 特征和标签
data.replace([np.inf, -np.inf], np.nan, inplace=True)
data = data.dropna()
features = data[['Return', 'LogVolume', 'BuyPower', 'IBS']].values
labels = data['Return'].shift(-AFTER_TICK_NUM).values  # 预测几天后的收益率
labels = labels[:-AFTER_TICK_NUM]  # 删除最后几个NaN
features = features[:-AFTER_TICK_NUM]  # 确保 特征 和 标签 的长度相同
print(f'labels len:{len(labels)}, features len:{len(features)}.')
print(np.any(np.isnan(features)), np.any(np.isinf(features)))
print(np.any(np.isnan(labels)), np.any(np.isinf(labels)))

# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征
SEQ_LEN = 30
X, y = [], []
max_index = len(features) - SEQ_LEN
for i in range(max_index):
	X.append(features[i:i + SEQ_LEN])
	y.append(labels[i + SEQ_LEN - 1])  # 取训练数据最后一个时间步的标签作为预测目标
X, y = np.array(X), np.array(y)
print(f'X shape: {X.shape}, y shape: {y.shape}')

# 将数据划分为 训练集 和 测试集
split_idx = int(len(X) - 256)  # 保留最后几个样本作为测试集
X_train_val, X_test = X[:split_idx], X[split_idx:]
y_train_val, y_test = y[:split_idx], y[split_idx:]

# 训练集 进一步划分 训练集 和 验证集
val_size = int(0.2 * len(X_train_val))  # 20%作为验证集
X_train, X_val = X_train_val[:-val_size], X_train_val[-val_size:]
y_train, y_val = y_train_val[:-val_size], y_train_val[-val_size:]
print(f'X_train shape: {X_train.shape}, X_val shape: {X_val.shape}, X_test shape: {X_test.shape}')
print(f'y_train shape: {y_train.shape}, y_val shape: {y_val.shape}, y_test shape: {y_test.shape}')


# 创建数据集
train_dataset = TensorDataset(
	torch.from_numpy(X_train).float(),
	torch.from_numpy(y_train).float()
)
val_dataset = TensorDataset(
	torch.from_numpy(X_val).float(),
	torch.from_numpy(y_val).float()
)
test_dataset = TensorDataset(
	torch.from_numpy(X_test).float(),
	torch.from_numpy(y_test).float()
)

# 创建DataLoader
batch_size = 32  # 一次训练多少样本
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)

# 定义模型 优化器 学习器
model = LSTMPredictor(
	sequence_dim=X_train.shape[2],
	hidden_dim=256
).to(device)
# 学习率lr控制参数更新的步长, ​权重衰减weight_decay抑制参数值过大
optimizer = torch.optim.Adam(model.parameters(), lr=1e-5, weight_decay=1e-4)
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
	optimizer, 
	mode='min',
	factor=0.5,
	patience=3
)
loss_fn = nn.L1Loss()
model_path = 'model.pth'

def train():
	# 早停机制
	best_loss = float('inf')
	patience = 10
	no_improve = 0
	for epoch in range(30):
		# --------------------- 训练 ---------------------
		model.train()  # 切换到训练模式, 启用 Batch Normalization 和 Dropout
		train_loss = 0.0
		for x_batch, y_batch in train_loader:
			x_batch, y_batch = x_batch.to(device), y_batch.to(device)
			optimizer.zero_grad()  # 梯度清零
			y_pred = model(x_batch)  # 前向传播
			loss = loss_fn(y_pred, y_batch)  # 计算loss
			loss.backward()  # 反向传播
			torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
			optimizer.step()  # 更新参数
			train_loss += (loss.item() * x_batch.size(0))
		train_loss /= len(train_loader.dataset)  # 计算平均训练损失

		# --------------------- 验证 ---------------------
		model.eval()  # 切换到验证模式, 禁用 Batch Normalization 和 Dropout
		val_loss = 0.0
		with torch.no_grad():
			for x_val, y_val in val_loader:
				x_val, y_val = x_val.to(device), y_val.to(device)
				y_pred_val = model(x_val)
				val_loss += loss_fn(y_pred_val, y_val).item() * x_val.size(0)
		val_loss /= len(val_loader.dataset)  # 计算平均验证损失
		scheduler.step(val_loss)  # 更新学习率
		
		print(f'Epoch {epoch}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

		# --------------------- 早停 ---------------------
		if val_loss < best_loss:
			best_loss = val_loss
			print('保存')
			torch.save(model.state_dict(), model_path)
			no_improve = 0
		else:
			no_improve += 1
			if no_improve >= patience:
				print(f'Early stopping at epoch {epoch}')
				break
	print('训练完成')

# 加载模型
if os.path.exists(model_path):
	print('正在加载模型...')
	model.load_state_dict(torch.load(model_path, weights_only=True))
	print('加载模型成功')
else:
	print('开始从零训练模型...')
	train()

# --------------------- 测试 ---------------------
model.eval()  # 切换到验证模式, 禁用 Batch Normalization 和 Dropout

# 创建存储窗口
test_preds = []
test_true = []
test_loss = 0.0
with torch.no_grad():
	for x_test, y_test in test_loader:
		x_test, y_test = x_test.to(device), y_test.to(device)
		y_pred_test = model(x_test)
		test_preds.append(y_pred_test.cpu().numpy())
		test_true.append(y_test.cpu().numpy())
		test_loss += loss_fn(y_pred_test, y_test).item() * x_test.size(0)
test_loss /= len(test_loader.dataset)
print(f'Test Loss: {test_loss:.6f}')

# --------------------- 画图 ---------------------
test_preds = np.concatenate(test_preds)
test_true = np.concatenate(test_true)
print(f'test_preds shape: {test_preds.shape}, test_true shape: {test_true.shape}')
# 创建时间轴（使用测试集样本数）
time_steps = np.arange(len(test_true))
# 创建画布
plt.figure(figsize=(14, 6))
# 绘制实际值和预测值曲线
plt.plot(time_steps, test_preds, label='Predicted Returns', alpha=0.7)
plt.plot(time_steps, test_true, label='Actual Returns', alpha=0.7)
# 添加统计指标
mse = ((test_true - test_preds) ** 2).mean()
corr = np.corrcoef(test_true, test_preds)[0, 1]
plt.title(f"Return Prediction (MSE: {mse:.4f}, Corr: {corr:.4f})")
plt.xlabel("Time Steps")
plt.ylabel("Log Returns")
plt.legend()
plt.show()
print('绘制完成')