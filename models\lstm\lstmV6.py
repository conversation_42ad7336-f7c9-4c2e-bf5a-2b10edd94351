"""
这个版本修复了:
- dataloader
- 改到显卡上执行
"""
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
from torch.utils.data import TensorDataset, DataLoader
from scaler import CustomScaler

# 读取数据
filepath = './big_data/THE_USDT.csv'
df = pd.read_csv(filepath)

# 添加设备检测
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'Using device: {device}')
model_path = 'model_lstm.pth'

# 构造特征
features = pd.DataFrame()
features['BasePrice'] = df['close'].shift(1)
features['Return'] = np.log(df['close'] / (df['close'].shift(1) + 1e-8))  # ln(P(t)/P(t-1))
features['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5
features['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
features.replace([np.inf, -np.inf], np.nan, inplace=True)
features = features.dropna()
X = features.drop(columns=['BasePrice'])


scaler = CustomScaler()
normalized_X = scaler.fit_transform(X)

lookback = 20  # seq_len
test_set_size = 1000
train_set_size = len(normalized_X) - lookback - test_set_size
val_set_size = int(0.1 * train_set_size)

def split_data(data):
    data_raw = data.to_numpy() # convert to numpy array
    data_seq = []
    for index in range(len(data_raw) - lookback): 
        data_seq.append(data_raw[index: index + lookback])
    data_seq = np.array(data_seq)
    return_idx = data.columns.get_loc('Return')

    train_set_size_adj = train_set_size - val_set_size
    x_train = data_seq[:train_set_size_adj, :-1, :]
    y_train = data_seq[:train_set_size_adj, -1, return_idx]

    x_val = data_seq[train_set_size_adj:train_set_size, :-1, :]
    y_val = data_seq[train_set_size_adj:train_set_size, -1, return_idx]

    x_test = data_seq[train_set_size:, :-1, :]
    y_test = data_seq[train_set_size:, -1, return_idx]
    return [x_train, y_train, x_val, y_val, x_test, y_test]

x_train, y_train, x_val, y_val, x_test, y_test = split_data(normalized_X)
print('x_train.shape = ', x_train.shape)
print('y_train.shape = ', y_train.shape)
print('x_val.shape = ', x_val.shape)
print('y_val.shape = ', y_val.shape)
print('x_test.shape = ', x_test.shape)
print('y_test.shape = ', y_test.shape)

# 创建数据集
train_dataset = TensorDataset(
    torch.from_numpy(x_train).float().to(device),
    torch.from_numpy(y_train).float().to(device)
)
val_dataset = TensorDataset(
    torch.from_numpy(x_val).float().to(device),
    torch.from_numpy(y_val).float().to(device)
)
test_dataset = TensorDataset(
    torch.from_numpy(x_test).float().to(device),
    torch.from_numpy(y_test).float().to(device) 
)

# 创建DataLoader
batch_size = 128
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

input_dim = len(X.columns)
hidden_dim = 32
num_layers = 2
output_dim = 1
num_epochs = 1

class LSTM(nn.Module):
    def __init__(self, input_dim, hidden_dim, num_layers, output_dim):
        super(LSTM, self).__init__()
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_dim, output_dim)

    def forward(self, x):
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).to(device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).to(device)
        out, _ = self.lstm(x, (h0.detach(), c0.detach()))
        out = self.fc(out[:, -1, :]) 
        return out.squeeze()


# ---------------------------------------- 训练模型开始 ---------------------------------------- 
model = LSTM(input_dim=input_dim, hidden_dim=hidden_dim, output_dim=output_dim, num_layers=num_layers).to(device)
criterion = torch.nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=0.01)
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, 
    mode='min',
    factor=0.5,
    patience=5
)

import os, time
start_time = time.time()
best_val_loss = float('inf')
patience = 10
trigger_times = 0

if os.path.exists(model_path):
    print('加载模型')
    model.load_state_dict(torch.load(model_path, weights_only=True))

for epoch in range(num_epochs):
	# 训练阶段
	model.train()
	train_loss = 0.0
	for batch_x, batch_y in train_loader:
		# 前向传播
		y_train_pred = model(batch_x)
		loss = criterion(y_train_pred, batch_y)
		# 反向传播和优化
		optimizer.zero_grad()
		loss.backward()
		optimizer.step()
		train_loss += loss.item() * batch_x.size(0)
	# 计算平均训练损失
	train_loss = train_loss / len(train_dataset)
	# 验证阶段
	model.eval()
	val_loss = 0.0
	with torch.no_grad():
		for batch_x, batch_y in val_loader:
			y_val_pred = model(batch_x)
			loss = criterion(y_val_pred, batch_y)
			val_loss += loss.item() * batch_x.size(0)
	# 计算平均验证损失
	val_loss = val_loss / len(val_dataset)
		
	# 学习率调整
	scheduler.step(val_loss)
	current_lr = optimizer.param_groups[0]['lr']
	
	print(f"Epoch: {epoch}, Train_Loss: {train_loss:.6f}, Val_Loss: {val_loss:.6f}, Lr: {current_lr}")

	if val_loss < best_val_loss:
		best_val_loss = val_loss
		trigger_times = 0
		torch.save(model.state_dict(), model_path)
	else:
		trigger_times += 1
		if trigger_times >= patience:
			print('Early Stoped')
			break
    
training_time = time.time()-start_time
print("Training time: {}".format(training_time))
# ---------------------------------------- 训练模型完成 ----------------------------------------


# ---------------------------------------- 验证模型开始 ----------------------------------------
model.load_state_dict(torch.load(model_path, weights_only=True))
model.eval()
# 收集所有测试预测结果
y_test_pred = []
y_test_true = []

with torch.no_grad():
    for batch_x, batch_y in test_loader:
        batch_pred = model(batch_x)
        y_test_pred.append(batch_pred)
        y_test_true.append(batch_y)

# 将结果转换为numpy数组
y_test_pred = torch.cat(y_test_pred).cpu().numpy()
y_test_true = torch.cat(y_test_true).cpu().numpy()

# 反标准化原始数据
def log_return_to_price(base_prices, ln_earns):
    # 将对数收益率转换为价格 P(T) = P(T-1) * e^ (ln(P(T)/P(T-1)))
    res = []
    for i, ln_earn in enumerate(ln_earns):
        Pt_1 = base_prices.iloc[i]
        Pt = Pt_1 * np.exp(ln_earn)
        res.append(Pt)
    return np.array(res)

def from_norm_ln_earn_to_price(base_prices, normalized_ln_earn):
    ln_earns = scaler.inverse_transform(normalized_ln_earn)
    original_prices = log_return_to_price(base_prices, ln_earns)
    return original_prices

base_prices = features['BasePrice'][-test_set_size-1:-1]
orig_prices = from_norm_ln_earn_to_price(base_prices, y_test_true)
pred_prices = from_norm_ln_earn_to_price(base_prices, y_test_pred)

# ---------------------------------------- 验证模型完成 ----------------------------------------



# ---------------------------------------- 绘制结果 ----------------------------------------
import seaborn as sns
sns.set_style("darkgrid")    

fig = plt.figure()
ax = sns.lineplot(x = range(len(orig_prices)), y = orig_prices, label="Actual Price", color='royalblue')
ax = sns.lineplot(x = range(len(pred_prices)), y = pred_prices, label="Predict Price", color='tomato')
ax.set_title('Stock price', size = 14, fontweight='bold')
ax.set_xlabel("Days", size = 14)
ax.set_ylabel("Price", size = 14)
ax.set_xticklabels('', size=10)


plt.savefig('training_results.png')
# ---------------------------------------- 绘制结果完成 ----------------------------------------