'''
这个版本关于因子的位置编码有问题
和NLP的Transformer模型有很大区别
股价预测容易碰撞
'''

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from torch.utils.data import TensorDataset, DataLoader
from common.scaler import CustomScaler

# 读取数据
filepath = './big_data/THE_USDT.csv'
df = pd.read_csv(filepath)

# 添加设备检测
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f'Using device: {device}')
model_path = 'model_trm.pth'

# 构造特征
features = pd.DataFrame()
features['BasePrice'] = df['close'].shift(1)
features['Return'] = np.log(df['close'] / (df['close'].shift(1) + 1e-8))  # ln(P(t)/P(t-1))
features['VolChg'] = np.log10(df['volume'] / (df['volume'].shift(1) + 1e-8))
features['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
features.replace([np.inf, -np.inf], np.nan, inplace=True)
features = features.dropna()
X = features.drop(columns=['BasePrice'])
print('X.shape = ', X.shape)

# 数据标准化
scaler = CustomScaler()
normalized_X = scaler.fit_transform(X)
# 数据集分割
lookback = 20
test_set_size = 1000
train_set_size = len(normalized_X) - lookback - test_set_size
val_set_size = int(0.1 * train_set_size)

def split_data(data):
    data_raw = data.to_numpy() # convert to numpy array
    data_seq = []
    for index in range(len(data_raw) - lookback): 
        data_seq.append(data_raw[index: index + lookback])
    data_seq = np.array(data_seq)
    return_idx = data.columns.get_loc('Return')

    train_set_size_adj = train_set_size - val_set_size
    x_train = data_seq[:train_set_size_adj, :-1, :]
    y_train = data_seq[:train_set_size_adj, -1, return_idx]

    x_val = data_seq[train_set_size_adj:train_set_size, :-1, :]
    y_val = data_seq[train_set_size_adj:train_set_size, -1, return_idx]

    x_test = data_seq[train_set_size:, :-1, :]
    y_test = data_seq[train_set_size:, -1, return_idx]
    return [x_train, y_train, x_val, y_val, x_test, y_test]

x_train, y_train, x_val, y_val, x_test, y_test = split_data(normalized_X)
print('x_train.shape = ', x_train.shape)
print('y_train.shape = ', y_train.shape)
print('x_val.shape = ', x_val.shape)
print('y_val.shape = ', y_val.shape)
print('x_test.shape = ', x_test.shape)
print('y_test.shape = ', y_test.shape)

# 创建数据集
train_dataset = TensorDataset(
    torch.from_numpy(x_train).float().to(device),
    torch.from_numpy(y_train).float().to(device)
)
val_dataset = TensorDataset(
    torch.from_numpy(x_val).float().to(device),
    torch.from_numpy(y_val).float().to(device)
)
test_dataset = TensorDataset(
    torch.from_numpy(x_test).float().to(device),
    torch.from_numpy(y_test).float().to(device) 
)
# 创建DataLoader
batch_size = 128
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

class PositionalEncoding(nn.Module):
    '''
    位置编码（用于注入序列顺序信息）
    '''
    def __init__(self, d_model, max_len=100):
        super().__init__()
        # 代表每个时间步的位置
        position = torch.arange(max_len).unsqueeze(1)
        # 控制不同维度的正弦/余弦频率
        div_term = torch.exp(torch.arange(0, d_model, 2) * (-np.log(10000.0) / d_model))
        # 最终的位置编码矩阵, 每一行是一个位置的编码
        pe = torch.zeros(max_len, d_model)
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        # 会和模型参数一起保存，但不会随着模型训练而改变
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0)]
        return x


class DecoderOnlyTransformer(nn.Module):
    def __init__(self, input_dim=5, d_model=64, nhead=4, num_layers=3):
        super().__init__()
        self.embedding = nn.Linear(input_dim, d_model)
        self.pos_encoder = PositionalEncoding(d_model)
        
        # 因果自注意力层（Decoder层）
        decoder_layer = nn.TransformerDecoderLayer(
            d_model=d_model, nhead=nhead, dim_feedforward=d_model*4
        )
        self.transformer = nn.TransformerDecoder(decoder_layer, num_layers=num_layers)
        
        self.output = nn.Linear(d_model, 1)
    
    def forward(self, x):
        # x: (batch_size, seq_len=19, input_dim)
        x = x.transpose(0, 1)  # (seq_len, batch_size, input_dim) (19, 128, 3)
        
        # 嵌入+位置编码
        x = self.embedding(x)  # (seq_len, batch_size, d_model) (19, 128, 64)
        print('x.shape:', x.shape)
        x = self.pos_encoder(x)  # 
        
        # 生成因果掩码（防止未来信息泄露）
        mask = self.generate_square_subsequent_mask(x.size(0)).to(x.device)
        
        # 通过TransformerDecoder（注意：这里用Decoder模拟自回归）
        # 因为只是预测单步输出，无需传统Decoder的memory输入
        x = self.transformer(x, x, tgt_mask=mask)  # (seq_len, batch_size, d_model)
        
        # 取最后一个时间步作为预测
        x = x[-1]  # (batch_size, d_model)
        return self.output(x)  # (batch_size, 1)
    
    def generate_square_subsequent_mask(self, sz):
        return torch.triu(torch.full((sz, sz), float('-inf')), diagonal=1)



# ---------------------------------------- 训练模型开始 ---------------------------------------- 
input_dim = len(X.columns)
num_layers = 3
output_dim = 1
num_epochs = 1
model = DecoderOnlyTransformer(input_dim=input_dim, d_model=64, num_layers=num_layers,)
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
    optimizer, 
    mode='min',
    factor=0.5,
    patience=5
)
import os, time
start_time = time.time()
best_val_loss = float('inf')
patience = 10
trigger_times = 0

if os.path.exists(model_path):
    print('加载模型')
    model.load_state_dict(torch.load(model_path, weights_only=True))

for epoch in range(num_epochs):
    # 训练阶段
    model.train()
    train_loss = 0.0
    for batch_x, batch_y in train_loader:
        print('batch_x.shape:', batch_x.shape)  # (128, 19, 3)
        y_train_pred = model(batch_x)
        loss = criterion(y_train_pred, batch_y)
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        train_loss += loss.item() * batch_x.size(0)
    # 计算平均训练损失
    train_loss = train_loss / len(train_dataset)
    # 验证阶段
    model.eval()
    val_loss = 0.0
    with torch.no_grad():
        for batch_x, batch_y in val_loader:
            y_val_pred = model(batch_x)
            loss = criterion(y_val_pred, batch_y)
            val_loss += loss.item() * batch_x.size(0)
    # 计算平均验证损失
    val_loss = val_loss / len(val_dataset)
    # 学习率调整
    scheduler.step(val_loss)
    current_lr = optimizer.param_groups[0]['lr']
    print(f"Epoch: {epoch}, Train_Loss: {train_loss:.6f}, Val_Loss: {val_loss:.6f}, Lr: {current_lr}")
    if val_loss < best_val_loss:
        best_val_loss = val_loss
        trigger_times = 0
        torch.save(model.state_dict(), model_path)
    else:
        trigger_times += 1
        if trigger_times >= patience:
            print('Early Stoped')
            break
    
training_time = time.time()-start_time
print("Training time: {}".format(training_time))


# ---------------------------------------- 验证模型开始 ----------------------------------------
model.load_state_dict(torch.load(model_path, weights_only=True))
model.eval()
# 收集所有测试预测结果
y_test_pred = []
y_test_true = []

with torch.no_grad():
    for batch_x, batch_y in test_loader:
        batch_pred = model(batch_x)
        y_test_pred.append(batch_pred)
        y_test_true.append(batch_y)

# 将结果转换为numpy数组
y_test_pred = torch.cat(y_test_pred).cpu().numpy()
y_test_true = torch.cat(y_test_true).cpu().numpy()

# 反标准化原始数据
def log_return_to_price(base_prices, ln_earns):
    # 将对数收益率转换为价格 P(T) = P(T-1) * e^ (ln(P(T)/P(T-1)))
    res = []
    for i, ln_earn in enumerate(ln_earns):
        Pt_1 = base_prices.iloc[i]
        Pt = Pt_1 * np.exp(ln_earn)
        res.append(Pt)
    return np.array(res)

def from_norm_ln_earn_to_price(base_prices, normalized_ln_earn):
    ln_earns = scaler.inverse_transform(normalized_ln_earn)
    original_prices = log_return_to_price(base_prices, ln_earns)
    return original_prices

base_prices = features['BasePrice'][-test_set_size-1:-1]
orig_prices = from_norm_ln_earn_to_price(base_prices, y_test_true)
pred_prices = from_norm_ln_earn_to_price(base_prices, y_test_pred)
# ---------------------------------------- 验证模型完成 ----------------------------------------

# ---------------------------------------- 绘制结果 ----------------------------------------
import seaborn as sns
sns.set_style("darkgrid")    

fig = plt.figure()
ax = sns.lineplot(x = range(len(orig_prices)), y = orig_prices, label="Actual Price", color='royalblue')
ax = sns.lineplot(x = range(len(pred_prices)), y = pred_prices, label="Predict Price", color='tomato')
ax.set_title('Stock price', size = 14, fontweight='bold')
ax.set_xlabel("Days", size = 14)
ax.set_ylabel("Price", size = 14)
ax.set_xticklabels('', size=10)


plt.savefig('test_results.png')
# ---------------------------------------- 绘制结果完成 ----------------------------------------