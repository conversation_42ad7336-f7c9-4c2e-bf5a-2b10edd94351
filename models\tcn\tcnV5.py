"""
这个版本的问题是:
1. 归一化方法不对
"""

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

from torch import nn
from torch.utils.data import TensorDataset, DataLoader
import torch
from torch.nn.utils.parametrizations import weight_norm

class Chomp1d(nn.Module):  
	# 裁剪多余填充的未来数据以保持因果性
	def __init__(self, chomp_size):
		super().__init__()
		self.chomp_size = chomp_size
		
	def forward(self, x):
		return x[:, :, :-self.chomp_size].contiguous()
	
class Permute(nn.Module):
    def __init__(self, dims):
        super().__init__()
        self.dims = dims
    def forward(self, x):
        return x.permute(*self.dims)

class TemporalBlock(nn.Module):
	
	def __init__(self, n_inputs, n_outputs, kernel_size, dilation):
		'''
		- n_inputs: 输入通道数
		- n_outputs: 输出通道数
		- kernel_size: 卷积核大小
		- dilation: 膨胀系数
		'''
		super().__init__()
		padding = (kernel_size-1) * dilation  # 因果卷积的填充计算
		self.conv = nn.Sequential(
			# weight_norm对权重矩阵做归一化
			weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),
			Chomp1d(padding),
			Permute((0, 2, 1)),
			nn.LayerNorm(n_outputs),  # 对每个样本的通道维度归一化
			Permute((0, 2, 1)),
			nn.ReLU(),
			nn.Dropout(0.4)
		)
		self.conv2 = nn.Sequential(
			weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),
			Chomp1d(padding),
			Permute((0, 2, 1)),
			nn.LayerNorm(n_outputs),
			Permute((0, 2, 1)),
			nn.ReLU(),
			nn.Dropout(0.4)
		)
		self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
		
	def forward(self, x):
		out = self.conv(x)
		out = self.conv2(out)
		res = x if self.downsample is None else self.downsample(x)
		return nn.ReLU()(out + res)  # 残差连接
	
class TCN(nn.Module):
	'''
	- in_channels: 输入通道数
	- num_channels: 共几层, 每层的输出通道数
	- kernel_size: 卷积核大小
	- output_size: 输出通道数
	'''
	def __init__(self, in_channels, num_channels, kernel_size=3, output_size=1):
		super().__init__()
		layers = []
		num_levels = len(num_channels)
		for i in range(num_levels):
			dilation = 2 ** i  # 指数级膨胀系数
			layers += [TemporalBlock(in_channels if i==0 else num_channels[i-1], 
									num_channels[i], kernel_size, dilation)]
		self.network = nn.Sequential(*layers)
		self.linear = nn.Linear(num_channels[-1], output_size)
		
	def forward(self, x):
		# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)
		x = x.permute(0, 2, 1)
		x = self.network(x)
		x = x[:, :, -1]  # 取序列的最后一个时间步 (200,32,30) -> (200,32)
		x = self.linear(x)  # 将输出传递给线性层 (200,32) -> (200,1)
		return x.squeeze()  # (200,1) -> (200)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 读取数据
df = pd.read_csv('./sample_data/XRP_USDT.csv')
# 计算收益率
data = pd.DataFrame()
data['Return'] = np.log(df['close'] / df['close'].shift(1))
# 计算成交量
data['LogVolume'] = np.log(df['volume'] + 1)
# 计算买卖方实力
data['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5
# 计算IBS
data['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)


# 预计几个tick后的收益率
AFTER_TICK_NUM = 15
# 准备 特征和标签
data.replace([np.inf, -np.inf], np.nan, inplace=True)
data = data.dropna()
features = data[['Return', 'LogVolume', 'BuyPower', 'IBS']].values
labels = data['Return'].shift(-AFTER_TICK_NUM).values  # 预测几天后的收益率
labels = labels[:-AFTER_TICK_NUM]  # 删除最后几个NaN
features = features[:-AFTER_TICK_NUM]  # 确保 特征 和 标签 的长度相同
print(f'labels len:{len(labels)}, features len:{len(features)}.')
print(np.any(np.isnan(features)), np.any(np.isinf(features)))
print(np.any(np.isnan(labels)), np.any(np.isinf(labels)))

# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征
SEQ_LEN = 30
X, y = [], []
max_index = len(features) - SEQ_LEN
for i in range(max_index):
	X.append(features[i:i + SEQ_LEN])
	y.append(labels[i + SEQ_LEN - 1])  # 取训练数据最后一个时间步的标签作为预测目标
X, y = np.array(X), np.array(y)
print(f'X shape: {X.shape}, y shape: {y.shape}')

# 将数据划分为 训练集 和 测试集
split_idx = int(len(X) - 256)  # 保留最后几个样本作为测试集
X_train_val, X_test = X[:split_idx], X[split_idx:]
y_train_val, y_test = y[:split_idx], y[split_idx:]

# 训练集 进一步划分 训练集 和 验证集
val_size = int(0.2 * len(X_train_val))  # 20%作为验证集
X_train, X_val = X_train_val[:-val_size], X_train_val[-val_size:]
y_train, y_val = y_train_val[:-val_size], y_train_val[-val_size:]
print(f'X_train shape: {X_train.shape}, X_val shape: {X_val.shape}, X_test shape: {X_test.shape}')
print(f'y_train shape: {y_train.shape}, y_val shape: {y_val.shape}, y_test shape: {y_test.shape}')


# 创建数据集
train_dataset = TensorDataset(
	torch.from_numpy(X_train).float(),
	torch.from_numpy(y_train).float()
)
val_dataset = TensorDataset(
	torch.from_numpy(X_val).float(),
	torch.from_numpy(y_val).float()
)
test_dataset = TensorDataset(
	torch.from_numpy(X_test).float(),
	torch.from_numpy(y_test).float()
)

# 创建DataLoader
batch_size = 32  # 一次训练多少样本
train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)
val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2, pin_memory=True)

# 定义模型 优化器 学习器
model = TCN(features.shape[-1], (32, 32, 32, 32)).to(device)
# 学习率lr控制参数更新的步长, ​权重衰减weight_decay抑制参数值过大
optimizer = torch.optim.Adam(model.parameters(), lr=1e-5, weight_decay=1e-4)
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
	optimizer, 
	mode='min',
	factor=0.5,
	patience=3
)
loss_fn = nn.L1Loss()
model_path = 'model_tcnV5.pth'

def train():
	# 早停机制
	best_loss = float('inf')
	patience = 10
	no_improve = 0
	for epoch in range(1000):
		# --------------------- 训练 ---------------------
		model.train()  # 切换到训练模式, 启用 Batch Normalization 和 Dropout
		train_loss = 0.0
		for x_batch, y_batch in train_loader:
			x_batch, y_batch = x_batch.to(device), y_batch.to(device)
			optimizer.zero_grad()  # 梯度清零
			y_pred = model(x_batch)  # 前向传播
			loss = loss_fn(y_pred, y_batch)  # 计算loss
			loss.backward()  # 反向传播
			torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
			optimizer.step()  # 更新参数
			train_loss += (loss.item() * x_batch.size(0))
		train_loss /= len(train_loader.dataset)  # 计算平均训练损失

		# --------------------- 验证 ---------------------
		model.eval()  # 切换到验证模式, 禁用 Batch Normalization 和 Dropout
		val_loss = 0.0
		with torch.no_grad():
			for x_val, y_val in val_loader:
				x_val, y_val = x_val.to(device), y_val.to(device)
				y_pred_val = model(x_val)
				val_loss += loss_fn(y_pred_val, y_val).item() * x_val.size(0)
		val_loss /= len(val_loader.dataset)  # 计算平均验证损失
		scheduler.step(val_loss)  # 更新学习率
		
		print(f'Epoch {epoch}, Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

		# --------------------- 早停 ---------------------
		if val_loss < best_loss:
			best_loss = val_loss
			print('保存')
			torch.save(model.state_dict(), model_path)
			no_improve = 0
		else:
			no_improve += 1
			if no_improve >= patience:
				print(f'Early stopping at epoch {epoch}')
				break
	print('训练完成')

# 加载模型
if os.path.exists(model_path):
	print('正在加载模型...')
	model.load_state_dict(torch.load(model_path, weights_only=True))
	print('加载模型成功')
else:
	print('开始从零训练模型...')
	train()

# --------------------- 测试 ---------------------
model.eval()  # 切换到验证模式, 禁用 Batch Normalization 和 Dropout

# 创建存储窗口
test_preds = []
test_true = []
test_loss = 0.0
with torch.no_grad():
	for x_test, y_test in test_loader:
		x_test, y_test = x_test.to(device), y_test.to(device)
		y_pred_test = model(x_test)
		test_preds.append(y_pred_test.cpu().numpy())
		test_true.append(y_test.cpu().numpy())
		test_loss += loss_fn(y_pred_test, y_test).item() * x_test.size(0)
test_loss /= len(test_loader.dataset)
print(f'Test Loss: {test_loss:.6f}')

# --------------------- 画图 ---------------------
test_preds = np.concatenate(test_preds)
test_true = np.concatenate(test_true)
print(f'test_preds shape: {test_preds.shape}, test_true shape: {test_true.shape}')
# 创建时间轴（使用测试集样本数）
time_steps = np.arange(len(test_true))
# 创建画布
plt.figure(figsize=(14, 6))
# 绘制实际值和预测值曲线
plt.plot(time_steps, test_preds, label='Predicted Returns', alpha=0.7)
plt.plot(time_steps, test_true, label='Actual Returns', alpha=0.7)
# 添加统计指标
mse = ((test_true - test_preds) ** 2).mean()
corr = np.corrcoef(test_true, test_preds)[0, 1]
plt.title(f"Return Prediction (MSE: {mse:.4f}, Corr: {corr:.4f})")
plt.xlabel("Time Steps")
plt.ylabel("Log Returns")
plt.legend()
plt.show()
print('绘制完成')