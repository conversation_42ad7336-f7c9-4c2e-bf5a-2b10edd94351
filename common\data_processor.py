import pandas as pd
import numpy as np
import torch
from torch.utils.data import TensorDataset, DataLoader

from .config import Config
from .scaler import CustomScaler

class DataProcessor:
    def __init__(self, config: Config, device: str):
        self.config = config
        self.device = device
        self.scaler = CustomScaler()
        self.raw_df = pd.DataFrame()
        self.features = pd.DataFrame()
        
    def load_data(self, filepath: str) -> pd.DataFrame:
        self.raw_df = pd.read_csv(filepath)
        return self.raw_df
    
    def build_features(self, df: pd.DataFrame=None) -> pd.DataFrame:
        self.features['BasePrice'] = df['close'].shift(1)
        self.features['Return'] = np.log(df['close'] / df['close'].shift(1) + 1e-8)
        self.features['VolChg'] = np.log10(df['volume'] / (df['volume'].shift(1) + 1e-8))
        self.features['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'] + 1e-8)
        self.features.replace([np.inf, -np.inf], np.nan, inplace=True)
        self.features = self.features.dropna()
        X = self.features.drop(columns=['BasePrice'])
        print('X.shape = ', X.shape)
        return self.features

    def split_data(self, data: pd.DataFrame) -> list:
        # 读取配置
        input_window = self.config.input_window
        output_window = self.config.output_window
        test_set_size = self.config.test_set_size
        scope = input_window + output_window
        train_set_size = len(data) - scope - test_set_size
        val_set_size = int(self.config.val_set_pct * train_set_size)
        # 构建数据集
        data_raw = data.to_numpy() # convert to numpy array
        data_seq = []
        for index in range(len(data_raw) - scope): 
            data_seq.append(data_raw[index: index + scope])
        data_seq = np.array(data_seq)
        return_idx = data.columns.get_loc('Return')

        train_set_size_adj = train_set_size - val_set_size
        x_train = data_seq[:train_set_size_adj, :-output_window, :]
        y_train = data_seq[:train_set_size_adj, -output_window:, return_idx]

        x_val = data_seq[train_set_size_adj:train_set_size, :-output_window, :]
        y_val = data_seq[train_set_size_adj:train_set_size, -output_window:, return_idx]

        x_test = data_seq[train_set_size:, :-output_window, :]
        y_test = data_seq[train_set_size:, -output_window:, return_idx]
        return [x_train, y_train, x_val, y_val, x_test, y_test]

    def build_dataloader(self, normalized_X: pd.DataFrame) -> list:
        x_train, y_train, x_val, y_val, x_test, y_test = self.split_data(normalized_X)
        print('x_train.shape = ', x_train.shape)
        print('y_train.shape = ', y_train.shape)
        print('x_val.shape = ', x_val.shape)
        print('y_val.shape = ', y_val.shape)
        print('x_test.shape = ', x_test.shape)
        print('y_test.shape = ', y_test.shape)
        # 创建数据集
        train_dataset = TensorDataset(
            torch.from_numpy(x_train).float().to(self.device),
            torch.from_numpy(y_train).float().to(self.device)
        )
        val_dataset = TensorDataset(
            torch.from_numpy(x_val).float().to(self.device),
            torch.from_numpy(y_val).float().to(self.device)
        )
        test_dataset = TensorDataset(
            torch.from_numpy(x_test).float().to(self.device),
            torch.from_numpy(y_test).float().to(self.device) 
        )
        batch_size = self.config.batch_size
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        return train_loader, val_loader, test_loader
    
    def normalize_data(self, X: pd.DataFrame) -> pd.DataFrame:
        normalized_X = self.scaler.fit_transform(X)
        return normalized_X

    def denormalize_data(self, normalized_ln_earn: np.ndarray, base_prices: np.ndarray) -> np.ndarray:
        ln_earns = self.scaler.inverse_transform(normalized_ln_earn)
        # 将对数收益率转换为价格 P(T) = P(T-1) * e^ (ln(P(T)/P(T-1)))
        res = []
        for i, ln_earn in enumerate(ln_earns):
            Pt_1 = base_prices.iloc[i]
            Pt = Pt_1 * np.exp(ln_earn)
            res.append(Pt)
        return np.array(res)

