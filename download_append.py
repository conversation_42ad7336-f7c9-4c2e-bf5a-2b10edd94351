import ccxt
import pandas as pd
import os
import time
from datetime import datetime, timedelta

# 初始化 Binance 交易所
exchange = ccxt.binance({
    'httpsProxy': 'http://172.22.48.1:7890',  # 设置代理
    'enableRateLimit': True,
})

# 定义交易对及其 开始时间
trading_pairs = {
    'XRP/USDT': '2020-12-01',
    'DOGE/USDT': '2020-12-01',
    'THE/USDT': '2024-11-30',
    'WLD/USDT': '2023-09-25',
}

# 定义下载数据的时间框架
timeframe = '1m'
limit = 500  # 每次获取的最大条数
output_dir = 'binance_data'  # 数据保存目录

# 确保保存目录存在
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

def fetch_and_append_ohlcv(exchange, symbol, start_date, start_offset=5000):
    """
    从指定日期的第 start_offset 根 K 线开始，下载到当前日期的分钟级数据，并逐批写入 CSV。
    """
    since = int((start_date + timedelta(minutes=start_offset)).timestamp() * 1000)  # 转换为毫秒
    end_timestamp = int(datetime.now().timestamp() * 1000)  # 当前时间戳（毫秒）
    output_file = os.path.join(output_dir, f"{symbol.replace('/', '_')}.csv")
    first_write = True if not os.path.exists(output_file) else False  # 检查是否需要写入表头

    while since < end_timestamp:
        try:
            # 获取数据
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since, limit)
            if not ohlcv:
                break  # 如果没有数据，停止

            # 转换为 DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'buy_volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')  # 转换时间戳为日期时间

            # 追加写入到 CSV 文件
            df.to_csv(output_file, mode='a', header=first_write, index=False)
            first_write = False  # 写入第一批后，不再写入表头

            # 更新 since 为最后一条数据的时间戳 + 1 毫秒
            since = ohlcv[-1][0] + 1
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            break

    print(f"Data for {symbol} saved to {output_file}.")

# 主逻辑：下载每个交易对的数据
for symbol, ico_date in trading_pairs.items():
    print(f"Fetching data for {symbol} starting from {ico_date}...")
    start_date = datetime.strptime(ico_date, '%Y-%m-%d')  # 将 ICO 日期解析为 datetime 对象
    data = fetch_and_append_ohlcv(exchange, symbol, start_date)

