import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

import torch

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# 转换为DataFrame
df = pd.read_csv('./sample_data/THE_USDT.csv')
# 将时间戳转换为日期格式
data = pd.DataFrame()

# 预计几个tick后的收益率
AFTER_TICK_NUM = 5
# 计算收益率
data['Return'] = np.log(df['close'] / (df['close'].shift(1) + 1e-8))
# 计算成交量
data['LogVolume'] = np.log(df['volume'] + 1)
data = data.dropna()
# 计算买卖方实力
data['BuyPower'] = df['buy_volume'] / (df['volume'] + 1e-8) - 0.5
# 计算IBS
data['IBS'] = (df['close'] - df['low']) / (df['high'] - df['low'])
# 准备 特征和标签
features = data[['Return', 'LogVolume', 'BuyPower', 'IBS']].values
labels = data['Return'].shift(-AFTER_TICK_NUM).dropna().values  # 预测10天后的收益率

# 确保 特征 和 标签 的长度相同
AFTER_TICK_NUM = 10
features = features[:-AFTER_TICK_NUM]
# 对特征进行标准化
scaler = StandardScaler()
features = scaler.fit_transform(features)

# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征
SQL_LEN = 30
X, y = [], []
for i in range(len(features) - SQL_LEN):
    X.append(features[i:i + SQL_LEN])
    y.append(labels[i + SQL_LEN - 1])  # 取训练数据最后一个时间步的标签作为预测目标
X, y = np.array(X), np.array(y)

# 将数据划分为 训练集 和 测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.02, random_state=42, shuffle=False)

from torch import nn
from torch.nn.utils.parametrizations import weight_norm
import torch

class Chomp1d(nn.Module):  
	# 裁剪多余填充的未来数据以保持因果性
	def __init__(self, chomp_size):
		super().__init__()
		self.chomp_size = chomp_size
		
	def forward(self, x):
		return x[:, :, :-self.chomp_size].contiguous()
	

class TemporalBlock(nn.Module):
	
	def __init__(self, n_inputs, n_outputs, kernel_size, dilation):
		'''
		- n_inputs: 输入通道数
		- n_outputs: 输出通道数
		- kernel_size: 卷积核大小
		- dilation: 膨胀系数
		'''
		super().__init__()
		padding = (kernel_size-1) * dilation  # 因果卷积的填充计算
		self.conv = nn.Sequential(
			# nn.Conv1d的padding是左右各填充padding个
			weight_norm(nn.Conv1d(n_inputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),
			Chomp1d(padding),
			nn.Sigmoid(),
			nn.Dropout(0.2)
		)
		self.conv2 = nn.Sequential(
			weight_norm(nn.Conv1d(n_outputs, n_outputs, kernel_size, padding=padding, dilation=dilation)),
			Chomp1d(padding),
			nn.Sigmoid(),
			nn.Dropout(0.2)
		)
		self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
		
	def forward(self, x):
		# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)
		out = self.conv(x)
		out = self.conv2(out)
		res = x if self.downsample is None else self.downsample(x)
		return nn.ReLU()(out + res)  # 残差连接
	

class TCN(nn.Module):
	'''
	- input_dim: 输入通道数
	- num_channels: 共几层, 每层的输出通道数
	- kernel_size: 卷积核大小
	- output_size: 输出通道数
	'''
	def __init__(self, input_dim, num_channels, kernel_size=3, output_size=1):
		super().__init__()
		layers = []
		num_levels = len(num_channels)
		for i in range(num_levels):
			dilation = 2 ** i  # 指数级膨胀系数
			layers += [TemporalBlock(input_dim if i==0 else num_channels[i-1], 
									num_channels[i], kernel_size, dilation)]
		self.network = nn.Sequential(*layers)
		self.linear = nn.Linear(num_channels[-1], output_size)
		
	def forward(self, x):
		# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)
		x = x.permute(0, 2, 1)
		x = self.network(x)
		x = x[:, :, -1]  # 取序列的最后一个时间步 (200,32,30) -> (200,32)
		x = self.linear(x)  # 将输出传递给线性层 (200,32) -> (200,1)
		return x.squeeze()  # (200,1) -> (200)
	
tcn = TCN(features.shape[-1], (32, 64, 32)).to(device)  # 实例化模型
optimizer = torch.optim.Adam(tcn.parameters(), lr=1e-5)
loss_fn = nn.L1Loss()

# 训练模型
epochs = 50
x = torch.from_numpy(X_train).float().to(device)
y = torch.from_numpy(y_train).float().to(device)
for epoch in range(epochs):
	# 前向传播
	y_pred = tcn(x)
	# 计算loss
	loss = loss_fn(y_pred, y)
	print(f'训练到第{epoch}次, loss:{loss}')
	# 梯度清零
	optimizer.zero_grad()
	# 反向传播
	loss.backward()
	# 更新参数
	optimizer.step()
print('训练完成')

# 预测
x_text_ = torch.from_numpy(X_test).float().to(device)
y_test_ = torch.from_numpy(y_test).float().to(device)
y_pred_ = tcn(x_text_)
# 转为numpy数组
y_test_np = y_test_.detach().cpu().numpy()
y_pred_np = y_pred_.detach().cpu().numpy()
# 画图对比
plt.figure(figsize=(10, 6))
plt.plot(y_test_np, label='Actual Returns')
plt.plot(y_pred_np, label='Predicted Returns')
plt.title('Comparison of Actual vs Predicted Returns')
plt.legend()
plt.show()