download:
	python download_during.py \
    --exchange binance \
    --symbol WLD/USDT:USDT \
    --start_date 2025-04-01 \
    --end_date 2025-04-15 \
    --output big_data/WLD_USDT_USDT.csv

lstm:
	python models/lstm/lstmV8.py

tcn:
	python models/tcn/tcn.py
	rm -f model_trm.pth

build_data:
	python models/trm/build_data.py

train:
	python models/trm/trmV2.py

bt:
	python backtest.py

live:
	python main.py

test:
	python test.py



