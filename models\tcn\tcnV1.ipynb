{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["这是第一版, 有几个缺陷\n", "- 没用残差网络\n", "- 卷积层级数不够\n", "- MLP层级数不够\n", "- K线数据不够, 没加主买量, ohl\n", "- 没加transformer\n", "- 训练数据不够多(下多点分钟级数据)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.model_selection import train_test_split\n", "import ccxt"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["服务器时间: 1741272381127\n"]}], "source": ["# Step 1: Fetch data\n", "exchange = ccxt.okx({\n", "    'httpsProxy': 'http://172.22.48.1:7890',  # 设置代理\n", "    'enableRateLimit': True,  # 启用速率限制\n", "})\n", "symbol = 'SOL/USDT'  # 交易对\n", "timeframe = '1d'     # 时间框架（1天）\n", "since = exchange.parse8601('2023-03-04T00:00:00Z')  # 开始时间（ISO 8601格式）\n", "limit = 365          # 限制为365条数据（1年）\n", "\n", "# 获取 Binance 服务器时间\n", "server_time = exchange.fetch_time()\n", "print(f\"服务器时间: {server_time}\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# 下载数据\n", "ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since, limit)\n", "\n", "# 转换为DataFrame\n", "df = pd.DataFrame(ohlcv, columns=['date', 'open', 'high', 'low', 'close', 'volume'])\n", "# 将时间戳转换为日期格式\n", "df['date'] = pd.to_datetime(df['date'], unit='ms')\n", "data = pd.DataFrame({\n", "\t'Date': df['date'],\n", "    'Price': df['close'],\n", "    'Volume': df['volume']\n", "})"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 计算收益率\n", "data['Return'] = np.log(data['Price'] / data['Price'].shift(1))\n", "# 计算波动率\n", "rolling_window = 10\n", "data['Volatility'] = data['Return'].rolling(window=rolling_window).std()\n", "# 计算成交量 TODO: 以后再加个主买量\n", "data['LogVolume'] = np.log(data['Volume'] + 1)\n", "data = data.dropna()\n", "# 准备 特征和标签 TODO: 建议再加上open, high, low\n", "features = data[['Return', 'Volatility', 'LogVolume']].values\n", "labels = data['Return'].shift(-rolling_window).dropna().values  # 预测10天后的收益率"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# 确保 特征 和 标签 的长度相同\n", "features = features[:-rolling_window]\n", "# 对特征进行标准化\n", "scaler = StandardScaler()\n", "features = scaler.fit_transform(features)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n"]}], "source": ["print(features.shape[-1])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 将特征数据转换为时间序列, 每个样本包含30个时间步的特征\n", "sequence_length = 30\n", "X, y = [], []\n", "for i in range(len(features) - sequence_length):\n", "    X.append(features[i:i + sequence_length])\n", "    y.append(labels[i + sequence_length - 1])  # 取训练数据最后一个时间步的标签作为预测目标\n", "X, y = np.array(X), np.array(y)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# 将数据划分为 训练集 和 测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch import nn\n", "\n", "# 定义模型\n", "class TCN(nn.Module):\n", "\tdef __init__(self, input_dim):\n", "\t\tsuper().__init__()\n", "\t\tself.conv1 = nn.Conv1d(in_channels=input_dim, out_channels=64, kernel_size=3, dilation=1)\n", "\t\tself.conv2 = nn.Conv1d(in_channels=64, out_channels=64, kernel_size=3, dilation=2)\n", "\t\tself.dropout = nn.Dropout(0.2)\n", "\t\tself.pool = nn.AdaptiveAvgPool1d(1)\n", "\t\tself.fc = nn.<PERSON><PERSON>(64, 1)\n", "\n", "\tdef forward(self, x: torch.Tensor):\n", "\t\t\"\"\"\n", "\t\t- batch_size: 每一个epoch处理多少个时间段\n", "\t\t- seq_len: 每个时间段包含几个tick\n", "\t\t- input_dim: 每个tick包含几个参数\n", "\t\t\"\"\"\n", "\t\t# (batch_size, seq_len, input_dim) -> (batch_size, input_dim, seq_len)\n", "\t\tx = x.permute(0, 2, 1)\n", "\t\tx = self.conv1(x)\n", "\t\tx = torch.relu(x)\n", "\t\tx = self.dropout(x)\n", "\t\tx = self.conv2(x)\n", "\t\tx = torch.relu(x)  \n", "\t\tx = self.dropout(x)\n", "\t\tx = self.pool(x)  # (batch_size, input_dim, 1)\n", "\t\tx = x.squeeze(-1)  # 把最后为1的维度去掉, 变成(batch_size, input_dim)\n", "\t\tx = self.fc(x)\n", "\t\treturn x    # (batch_size, 1)\n", "\t\n", "tcn = TCN(features.shape[-1])  # 实例化模型\n", "optimizer = torch.optim.Adam(tcn.parameters())\n", "loss_fn = nn.MSELoss()\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练到第0次, loss:0.014973568730056286\n", "训练到第100次, loss:0.0020070814061909914\n", "训练到第200次, loss:0.0019609066657721996\n", "训练到第300次, loss:0.0018602408235892653\n", "训练到第400次, loss:0.001878310926258564\n", "训练到第500次, loss:0.00159180024638772\n", "训练到第600次, loss:0.0016917955363169312\n", "训练到第700次, loss:0.0015707716811448336\n", "训练到第800次, loss:0.0015538133447989821\n", "训练到第900次, loss:0.001382329035550356\n", "训练完成\n"]}], "source": ["# 训练模型\n", "epochs = 1000\n", "x = torch.from_numpy(X_train).float()\n", "y = torch.from_numpy(y_train).float()\n", "for epoch in range(epochs):\n", "\t# 前向传播\n", "\ty_pred = tcn(x)\n", "\t# 计算loss\n", "\tloss = loss_fn(y_pred.squeeze(), y)\n", "\tif epoch % 100 == 0:\n", "\t\tprint(f'训练到第{epoch}次, loss:{loss}')\n", "\t# 梯度清零\n", "\toptimizer.zero_grad()\n", "\t# 反向传播\n", "\tloss.backward()\n", "\t# 更新参数\n", "\toptimizer.step()\n", "print('训练完成')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'torch.Tensor'>\n", "<class 'torch.Tensor'>\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 预测\n", "x_text_ = torch.from_numpy(X_test).float()\n", "y_test_ = torch.from_numpy(y_test).float()\n", "y_pred_ = tcn(x_text_)\n", "# 转为numpy数组\n", "print(type(y_test_))\n", "print(type(y_pred_))\n", "y_test_np = y_test_.detach().numpy()\n", "y_pred_np = y_pred_.detach().numpy()\n", "# 画图对比\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(y_test_np, label='Actual Returns')\n", "plt.plot(y_pred_np, label='Predicted Returns')\n", "plt.title('Comparison of Actual vs Predicted Returns')\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}