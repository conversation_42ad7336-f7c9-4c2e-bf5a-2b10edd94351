from common.base_model import BaseModel
import torch.nn as nn
import torch

class LSTMModel(BaseModel):
    def __init__(self, config):
        super().__init__(config)
        self.lstm = nn.LSTM(
            config.input_dim,
            config.hidden_dim,
            config.num_layers,
            batch_first=True
        )
        self.fc = nn.Linear(config.hidden_dim, config.output_dim)
        
    def forward(self, x):
        device = x.device
        h0 = torch.zeros(self.config.num_layers, x.size(0), 
                        self.config.hidden_dim).to(device).requires_grad_()
        c0 = torch.zeros(self.config.num_layers, x.size(0), 
                        self.config.hidden_dim).to(device).requires_grad_()
        out, _ = self.lstm(x, (h0.detach(), c0.detach()))
        return self.fc(out[:, -1, :])
