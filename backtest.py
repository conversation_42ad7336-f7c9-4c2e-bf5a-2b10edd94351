import torch
import pandas as pd
import numpy as np
from models.trm.trmV2 import TradeTransformer, engineer_features_X, rolling_normalize
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix
import seaborn as sns


def load_historical_data(csv_file: str) -> pd.DataFrame:
    """加载历史数据"""
    df = pd.read_csv(csv_file)
    if 'timestamp' in df.columns:
        df['datetime'] = pd.to_datetime(df['timestamp'])
    elif 'datetime' in df.columns:
        df['datetime'] = pd.to_datetime(df['datetime'])
    else:
        print("警告：未找到时间列，使用索引作为时间")
        df['datetime'] = pd.date_range(start='2024-01-01', periods=len(df), freq='5T')

    df = df.sort_values('datetime').reset_index(drop=True)
    return df

def create_target_labels(df: pd.DataFrame) -> np.ndarray:
    """
    创建目标标签：
    0: 跌 (下跌超过0.1%)
    1: 平 (变化在±0.1%以内)
    2: 涨 (上涨超过0.1%)
    """
    current_prices = df['close']
    future_prices1 = current_prices.shift(-1)
    future_prices2 = current_prices.shift(-2)
    future_prices3 = current_prices.shift(-3)
    future_prices = (future_prices1 + future_prices2 + future_prices3) / 3

    # 计算收益率
    returns = (future_prices - current_prices) / current_prices
    returns = returns.values  # 转换为numpy数组

    # 创建标签 (与训练时保持一致)
    labels = np.ones(len(returns))
    labels[returns >= 0.001] = 2   # 涨幅>=0.1%算涨
    labels[returns <= -0.001] = 0  # 跌幅<=-0.1%算跌

    # 最后几个样本无法计算未来收益，设为-1(无效)
    labels[-3:] = -1

    return labels.astype(int)

def backtest_model(model_path: str, csv_file: str, seq_len: int = 200):
    """回测模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
	# 加载历史数据
    print(f"加载数据: {csv_file}")
    df = load_historical_data(csv_file)
    print(f"原始数据形状: {df.shape}")

    # 特征工程
    print("开始特征工程...")
    features_df = engineer_features_X(df)
    print(f"特征工程完成，特征数量: {features_df.shape[1]}")
    
	# 选择模型需要的特征列
    X_features = features_df.dropna()
    print(f"选择特征后形状: {X_features.shape}")

    # 滚动标准化
    print("开始滚动标准化...")
    X_normalized, valid_start = rolling_normalize(X_features.values, window=200)
    print(f"滚动标准化完成，形状: {X_normalized.shape}, 跳过了前 {valid_start} 个样本")

    # 转换回DataFrame保持索引
    X_features = pd.DataFrame(X_normalized,
                             columns=X_features.columns,
                             index=X_features.index[valid_start:])

    # 创建目标标签（对应标准化后的数据）
    valid_df = df.iloc[len(df) - len(X_features):]  # 对应特征工程后的数据
    y_true = create_target_labels(valid_df)

    # 去掉最后3个无效样本
    X_features = X_features.iloc[:-3]
    y_true = y_true[:-3]

    # 加载模型
    model = TradeTransformer(
        input_dim=X_features.shape[1],  # 使用实际特征数量
        pos_dim=16,
        model_dim=64,
        nhead=4,
        num_layers=2,
        num_classes=3,
        max_seq_len=200
    ).to(device)

    print(f"加载模型: {model_path}")
    model.load_state_dict(torch.load(model_path, weights_only=True, map_location=device))
    model.eval()

    

    # 批量预测
    predictions = []
    true_labels = []
    prediction_probs = []

    print("开始批量预测...")
    with torch.no_grad():
        for i in range(seq_len, len(X_features)):
            # 获取序列数据
            X_seq = X_features.iloc[i-seq_len:i].values  # [seq_len, input_dim]
            X_tensor = torch.FloatTensor(X_seq).unsqueeze(0).to(device)  # [1, seq_len, input_dim]
            # 模型预测
            logits = model(X_tensor)  # [1, 3]
            probs = torch.softmax(logits, dim=-1)  # [1, 3]
            pred_class = int(torch.argmax(probs, dim=-1).item())
            # 保存结果
            predictions.append(pred_class)
            true_labels.append(y_true[i])
            prediction_probs.append(probs[0].cpu().numpy())

    predictions = np.array(predictions)
    true_labels = np.array(true_labels)
    prediction_probs = np.array(prediction_probs)

    # 计算准确率
    accuracy = np.mean(predictions == true_labels) * 100
    print(f"\n回测结果:")
    print(f"总预测次数: {len(predictions)}")
    print(f"整体准确率: {accuracy:.2f}%")

    # 分类别准确率
    class_names = ['跌', '平', '涨']
    for i in range(3):
        mask = (true_labels == i)
        if np.sum(mask) > 0:
            class_acc = np.mean(predictions[mask] == i) * 100
            print(f"{class_names[i]}类准确率: {class_acc:.2f}% ({np.sum(mask)}个样本)")

    # 预测分布 vs 真实分布
    pred_dist = np.bincount(predictions, minlength=3)
    true_dist = np.bincount(true_labels, minlength=3)
    print(f"\n预测分布: 跌={pred_dist[0]}, 平={pred_dist[1]}, 涨={pred_dist[2]}")
    print(f"真实分布: 跌={true_dist[0]}, 平={true_dist[1]}, 涨={true_dist[2]}")

    # 绘制结果
    plot_backtest_results(predictions, true_labels, prediction_probs, class_names)

    return accuracy, predictions, true_labels, prediction_probs

def plot_backtest_results(predictions, true_labels, prediction_probs, class_names):
    """绘制回测结果"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文
    plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 1. 准确率随时间变化
    window_size = 100
    rolling_acc = []
    for i in range(window_size, len(predictions)):
        window_acc = np.mean(predictions[i-window_size:i] == true_labels[i-window_size:i]) * 100
        rolling_acc.append(window_acc)

    axes[0, 0].plot(rolling_acc)
    axes[0, 0].set_title(f'滚动准确率 (窗口={window_size})')
    axes[0, 0].set_ylabel('准确率 (%)')
    axes[0, 0].grid(True)

    # 2. 混淆矩阵
    try:
        

        cm = confusion_matrix(true_labels, predictions)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                    xticklabels=class_names, yticklabels=class_names, ax=axes[0, 1])
        axes[0, 1].set_title('混淆矩阵')
        axes[0, 1].set_ylabel('真实标签')
        axes[0, 1].set_xlabel('预测标签')
    except ImportError:
        print("警告：未安装sklearn或seaborn，跳过混淆矩阵绘制")
        axes[0, 1].text(0.5, 0.5, '需要安装sklearn和seaborn', ha='center', va='center')

    # 3. 预测置信度分布
    max_probs = np.max(prediction_probs, axis=1)
    axes[1, 0].hist(max_probs, bins=50, alpha=0.7)
    axes[1, 0].set_title('预测置信度分布')
    axes[1, 0].set_xlabel('最大概率')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True)

    # 4. 各类别预测概率分布
    for i, class_name in enumerate(class_names):
        axes[1, 1].hist(prediction_probs[:, i], bins=30, alpha=0.5, label=f'{class_name}类概率')
    axes[1, 1].set_title('各类别预测概率分布')
    axes[1, 1].set_xlabel('概率')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].legend()
    axes[1, 1].grid(True)

    plt.tight_layout()
    plt.savefig('backtest_results.png', dpi=300, bbox_inches='tight')

if __name__ == '__main__':
    # 配置参数
    model_path = './model_trm.pth'  # 新训练的模型路径
    csv_file = './big_data/AAPL_US_5min.csv'  # 使用与训练相同的数据集
    seq_len = 200  # 序列长度

    print("开始回测...")
    accuracy, predictions, true_labels, probs = backtest_model(model_path, csv_file, seq_len)
    print(f"\n回测完成！整体准确率: {accuracy:.2f}%")