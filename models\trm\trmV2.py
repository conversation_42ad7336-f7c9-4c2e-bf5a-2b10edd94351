import os
import time
import torch
import torch.nn as nn
import numpy as np
import pandas as pd


class TradeTransformer(nn.Module):
	def __init__(self, input_dim, pos_dim, model_dim, nhead, num_layers, num_classes, max_seq_len):
		super().__init__()
		self.input_proj = nn.Linear(input_dim, model_dim - pos_dim)
		# max_seq_len代表最多能处理max_seq_len个时间步的数据, 应>=seq_len, 后面序列变长就不需要重新训练
		self.pos_encoding = nn.Parameter(torch.randn(1, max_seq_len, pos_dim))  # 可学习的位置编码

		encoder_layer = nn.TransformerEncoderLayer(
			d_model=model_dim,
			nhead=nhead,
			batch_first=True,
			dropout=0.1,  # 添加dropout
			activation='gelu'  # 使用GELU激活函数
		)
		self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)

		# 添加dropout和更好的最终分类层
		self.dropout = nn.Dropout(0.1)
		self.fc = nn.Linear(model_dim, num_classes)

		# 权重初始化
		self._init_weights()

	def _init_weights(self):
		"""改进的权重初始化"""
		for name, param in self.named_parameters():
			if 'weight' in name and param.dim() > 1:
				nn.init.xavier_uniform_(param)
			elif 'bias' in name:
				nn.init.constant_(param, 0)

		# 位置编码使用较小的初始化
		nn.init.normal_(self.pos_encoding, mean=0, std=0.02)

	def forward(self, x):  # x: [batch, seq_len, input_dim]
		x_embed = self.input_proj(x)                     # [batch, seq_len, model_dim - pos_dim]
		pos = self.pos_encoding[:, :x.size(1), :]        # [1, seq_len, pos_dim]
		pos = pos.repeat(x.size(0), 1, 1)  # 位置编码对所有样本都是相同的, 所以每一个维度重复样本数次
		x = torch.cat([x_embed, pos], dim=-1)            # 拼接： [batch, seq_len, model_dim]
		out = self.transformer(x)  # shape: [batch, seq_len, model_dim]
		out = self.dropout(out[:, -1, :])  # 在最终分类前应用dropout
		logits = self.fc(out)
		'''
		第1列: 类别0的logits (跌)
		第2列: 类别1的logits (平)
		第3列: 类别2的logits (涨)
		'''
		return logits  # 返回原始logits，CrossEntropyLoss会自动应用softmax
	

class LSTM(nn.Module):
	def __init__(self, input_dim, hidden_dim, num_layers, output_dim, device, dropout=0.2):
		super().__init__()
		self.hidden_dim = hidden_dim
		self.num_layers = num_layers
		self.lstm = nn.LSTM(input_dim, hidden_dim, num_layers, batch_first=True, dropout=dropout if num_layers > 1 else 0)
		self.dropout = nn.Dropout(dropout)
		self.fc1 = nn.Linear(hidden_dim, hidden_dim // 2)
		self.fc2 = nn.Linear(hidden_dim // 2, output_dim)
		self.device = device

		# 改进权重初始化
		self._init_weights()

	def _init_weights(self):
		"""改进的权重初始化"""
		for name, param in self.named_parameters():
			if 'weight_ih' in name:
				torch.nn.init.xavier_uniform_(param.data)
			elif 'weight_hh' in name:
				torch.nn.init.orthogonal_(param.data)
			elif 'bias' in name:
				param.data.fill_(0)
				# LSTM的遗忘门偏置设为1
				n = param.size(0)
				param.data[(n//4):(n//2)].fill_(1)

	def forward(self, x):
		h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).to(self.device)
		c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_dim).to(self.device)
		out, _ = self.lstm(x, (h0.detach(), c0.detach()))
		# 使用最后一个时间步的输出
		last_output = out[:, -1, :]  # [batch_size, hidden_dim]
		# 通过全连接层
		x = torch.relu(self.fc1(last_output))
		x = self.dropout(x)
		logits = self.fc2(x)  # 输出三个类别的 logits
		return logits  # 输出 logits，CrossEntropyLoss 会自动应用 softmax


def sigmoid(x, scale=1.0):
	return 1.0 / (1.0 + np.exp(-scale * x))


def split_data(X, y, dates, train_ratio=0.7, val_ratio=0.15):
	"""
	分割数据为训练集、验证集、测试集

	Args:
		X: 特征数据
		y: 标签数据
		dates: 日期数据
		train_ratio: 训练集比例
		val_ratio: 验证集比例

	Returns:
		分割后的数据
	"""
	n_samples = len(X)
	train_size = int(n_samples * train_ratio)
	val_size = int(n_samples * val_ratio)

	# 按时间顺序分割（不打乱）
	X_train = X[:train_size]
	y_train = y[:train_size]
	dates_train = dates[:train_size]

	X_val = X[train_size:train_size + val_size]
	y_val = y[train_size:train_size + val_size]
	dates_val = dates[train_size:train_size + val_size]

	X_test = X[train_size + val_size:]
	y_test = y[train_size + val_size:]
	dates_test = dates[train_size + val_size:]

	return X_train, X_val, X_test, y_train, y_val, y_test, dates_train, dates_val, dates_test


def rolling_normalize(data, window=200):
	"""
	使用滚动窗口进行标准化，避免未来数据泄漏
	跳过前期数据不足的样本，确保标准化质量

	Args:
		data: numpy array, shape [时间步数, 特征数]
		window: int, 滚动窗口大小

	Returns:
		normalized_data: 标准化后的数据（从第window个样本开始）
		valid_indices: 有效数据的索引范围
	"""
	# 只处理有足够历史数据的样本
	valid_start = window - 1  # 从第window个样本开始（索引为window-1）
	valid_data = data[valid_start:]
	normalized = np.zeros_like(valid_data)

	for i in range(len(valid_data)):
		# 当前在原数据中的位置
		current_idx = valid_start + i
		# 用过去window天的数据计算统计量
		train_data = data[current_idx-window+1:current_idx+1]
		# 计算均值和标准差
		mean = np.mean(train_data, axis=0)
		std = np.std(train_data, axis=0)
		# 避免标准差为0的情况
		std = np.where(std == 0, 1, std)
		# 标准化当前时间点的数据
		normalized[i] = (valid_data[i] - mean) / std
	return normalized, valid_start



def detect_fvg_signals(df, lookback=20):
	"""检测FVG交易信号"""
	n = len(df)
	fvg_bull_signal = np.zeros(n)
	fvg_bear_signal = np.zeros(n)

	for i in range(lookback + 2, n):
		current_close = df['close'].iloc[i]

		# 检查过去lookback个周期内的FVG
		for j in range(i - lookback, i - 2):
			if j < 2:
				continue

			# 检测看涨FVG (第j-2根K线的high < 第j根K线的low)
			if df['high'].iloc[j-2] < df['low'].iloc[j]:
				fvg_top = df['low'].iloc[j]
				fvg_bottom = df['high'].iloc[j-2]

				# 当前价格是否进入FVG区域且发生回调
				if fvg_bottom <= current_close <= fvg_top:
					# 检查是否有回调确认（最近3根K线有下跌后反弹）
					recent_lows = df['low'].iloc[i-2:i+1]
					if len(recent_lows) >= 2 and recent_lows.min() < current_close:
						fvg_bull_signal[i] = 1
						break

			# 检测看跌FVG (第j-2根K线的low > 第j根K线的high)
			if df['low'].iloc[j-2] > df['high'].iloc[j]:
				fvg_top = df['low'].iloc[j-2]
				fvg_bottom = df['high'].iloc[j]

				# 当前价格是否进入FVG区域且发生回调
				if fvg_bottom <= current_close <= fvg_top:
					# 检查是否有回调确认（最近3根K线有上涨后回落）
					recent_highs = df['high'].iloc[i-2:i+1]
					if len(recent_highs) >= 2 and recent_highs.max() > current_close:
						fvg_bear_signal[i] = 1
						break

	return fvg_bull_signal, fvg_bear_signal


def engineer_features_X(df: pd.DataFrame) -> pd.DataFrame:
    features = pd.DataFrame(index=df.index)

    # ===== 振幅与收盘位置 =====
    features['price_range'] = (df['high'] - df['low']) / df['close']
    high_low_diff = df['high'] - df['low']
    features['price_position'] = np.where(high_low_diff == 0, 1,
                                          (df['close'] - df['low']) / high_low_diff)

    # ===== 多周期均线比 =====
    for period_s, period_l in [(5, 20), (20, 60), (20, 200)]:
        ma_s = df['close'].rolling(period_s).mean()
        ma_l = df['close'].rolling(period_l).mean()
        features[f'ma_ratio_{period_s}_{period_l}'] = ma_s / ma_l

    # ===== 成交量特征（多周期量比） =====
    for period in [5, 20, 200]:
        volume_ma = df['volume'].rolling(period).mean()
        features[f'volume_ratio_{period}'] = np.where(volume_ma == 0, 1, df['volume'] / volume_ma)

    # ===== VWAP（多周期） =====
    typical_price = (df['high'] + df['low'] + df['close']) / 3
    for period in [5, 20, 200]:
        pv_sum = (typical_price * df['volume']).rolling(period).sum()
        volume_sum = df['volume'].rolling(period).sum()
        vwap = pv_sum / volume_sum
        features[f'vwap_distance_{period}'] = (df['close'] - vwap) / df['close']

    # ===== 波动率特征（多周期） =====
    for period in [5, 20, 200]:
        features[f'volatility_{period}'] = df['close'].rolling(period).std()

    # ===== RSI（多周期） =====
    delta = df['close'].diff()
    for period in [5, 20, 200]:
        gain = (delta.where(delta > 0, 0)).rolling(period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
        rs = np.where(loss == 0, 100, gain / loss)
        features[f'rsi_{period}'] = 100 - (100 / (1 + rs))

    # ===== 突破类特征 =====
    for period in [5, 20, 200]:
        features[f'is_new_high_{period}'] = (df['close'] > df['high'].shift(1).rolling(period).max()).astype(int)
        features[f'is_new_low_{period}'] = (df['close'] < df['low'].shift(1).rolling(period).min()).astype(int)

    # ===== K线形态特征 =====
    features['upper_shadow'] = (df['high'] - np.maximum(df['close'], df['open'])) / df['close']
    features['lower_shadow'] = (np.minimum(df['close'], df['open']) - df['low']) / df['close']
    features['close_open_range'] = (df['close'] - df['open']) / df['close']

    # ===== 连续上涨/下跌（多周期） =====
    for period in [5, 20, 200]:
        features[f'up_k_count_{period}'] = (df['close'] > df['close'].shift(1)).rolling(period).sum()
        features[f'down_k_count_{period}'] = (df['close'] < df['close'].shift(1)).rolling(period).sum()

    # ===== 换手率特征 =====
    for period in [5, 20, 200]:
        turnover_ma = df['turnover'].rolling(period).mean()
        features[f'turnover_ratio_{period}'] = np.where(turnover_ma == 0, 1, df['turnover'] / turnover_ma)

    # ===== ATR波动特征（多周期） =====
    true_range = np.maximum(df['high'] - df['low'],
                            np.maximum(abs(df['high'] - df['close'].shift(1)),
                                       abs(df['low'] - df['close'].shift(1))))
    for period in [5, 20, 200]:
        features[f'atr_{period}'] = true_range.rolling(period).mean()

	# ===== FVG公平价值缺口交易信号 =====
    fvg_bull, fvg_bear = detect_fvg_signals(df)
    features['fvg_bull_signal'] = fvg_bull
    features['fvg_bear_signal'] = fvg_bear

    return features



def engineer_features(df):
	"""
	股票特征工程

	注意：此函数会因为计算未来收益率而丢失最后3行数据

	Args:
		df: 包含 ['open', 'high', 'low', 'close', 'volume', 'turnover'] 的原始DataFrame

	Returns:
		features: numpy array, 工程化后的特征
	"""
	features = engineer_features_X(df)

	# 目标变量：未来3日平均涨跌（分类）
	future_return_1d = df['close'].shift(-1) / df['close'] - 1  # 明天
	future_return_2d = df['close'].shift(-2) / df['close'] - 1  # 后天
	future_return_3d = df['close'].shift(-3) / df['close'] - 1  # 大后天
	future_return_avg = (future_return_1d + future_return_2d + future_return_3d) / 3  # 未来3日平均收益率

	# 基于绝对阈值的目标变量构造（保持市场真实分布）
	print("原始收益率统计:")
	print(f"  最小值: {future_return_avg.min():.6f}")
	print(f"  最大值: {future_return_avg.max():.6f}")
	print(f"  均值: {future_return_avg.mean():.6f}")
	print(f"  标准差: {future_return_avg.std():.6f}")

	# 使用更小的阈值来增加涨跌样本，减少"平"类占比
	threshold = 0.001

	def assign_label_absolute(return_val):
		if pd.isna(return_val):
			return np.nan
		elif return_val <= -threshold:
			return 0  # 跌
		elif return_val >= threshold:
			return 2  # 涨
		else:
			return 1  # 平（小幅波动）

	# 将收益率转换为类别标签
	features['target'] = future_return_avg.apply(assign_label_absolute)

	# 统计真实的类别分布
	target_counts = features['target'].value_counts().sort_index()
	total_valid = target_counts.sum()
	print("真实市场分布:")
	for label in [0, 1, 2]:
		count = target_counts.get(label, 0)
		pct = count / total_valid * 100 if total_valid > 0 else 0
		label_name = ['跌', '平', '涨'][label]
		print(f"  类别{label} ({label_name}): {count} 样本 ({pct:.1f}%)")

	# 分析NaN的来源
	print("NaN分析:")
	for col in features:
		nan_count = features[col].isna().sum()
		if nan_count > 0:
			print(f"  {col}: {nan_count} 个NaN")

	# 删除包含NaN的行（主要是最后3行因为target计算导致的NaN）
	features_clean = features.dropna()

	print(f"特征工程统计:")
	print(f"  原始数据: {len(df)} 行")
	print(f"  删除NaN后: {len(features_clean)} 行")
	print(f"  数据保留率: {len(features_clean)/len(df)*100:.1f}%")

	# 分析目标分布（现在target是类别标签0,1,2）
	target_values = np.array(features_clean['target'].values, dtype=float)
	print(f"目标分布分析:")
	print(f"  最小值: {target_values.min():.4f}")
	print(f"  最大值: {target_values.max():.4f}")
	print(f"  均值: {target_values.mean():.4f}")
	print(f"  标准差: {target_values.std():.4f}")
	print(f"  类别0 (跌): {(target_values == 0).sum()} ({(target_values == 0).mean()*100:.1f}%)")
	print(f"  类别1 (平): {(target_values == 1).sum()} ({(target_values == 1).mean()*100:.1f}%)")
	print(f"  类别2 (涨): {(target_values == 2).sum()} ({(target_values == 2).mean()*100:.1f}%)")

	return features_clean.values


def prepare_stock_data(csv_file, seq_len=60, window=200):
	"""
	完整的股票数据预处理流程

	Args:
		csv_file: CSV文件路径，包含 ['datetime', 'open', 'high', 'low', 'close', 'volume']
		seq_len: 序列长度
		window: 滚动标准化窗口，如果为None则自动设置为seq_len (避免数据泄漏)

	Returns:
		X: 输入特征 [样本数, seq_len, input_dim]
		y: 目标标签 [样本数]
		feature_dim: 特征维度数
		dates: 对应的日期列表 [样本数]
	"""
	# 1. 读取数据
	df = pd.read_csv(csv_file)
	df['datetime'] = pd.to_datetime(df['datetime'])
	df = df.sort_values('datetime').reset_index(drop=True)
	print(f"原始数据形状: {df.shape}")

	# 2. 特征工程
	features = engineer_features(df)
	print(f"特征工程后形状: {features.shape}")

	# 3. 分离特征和目标
	X_features = features[:, :-1]
	y_target = features[:, -1]  # 移除多余的维度，直接取一维数组

	# 4. 滚动标准化（避免数据泄漏，跳过数据不足的样本）
	print("开始滚动标准化...")
	X_normalized, valid_start = rolling_normalize(X_features, window=window)
	print(f"标准化完成，跳过了前 {valid_start} 个数据不足的样本")
	# 对应调整目标变量
	y_target_valid = y_target[valid_start:]
	print(f"有效数据形状: X={X_normalized.shape}, y={y_target_valid.shape}")

	# 5. 创建时间序列样本 (直接分离，避免多余步骤)
	X_final = []
	y_final = []
	for i in range(seq_len, len(X_normalized)):
		# 输入：过去seq_len天的特征数据
		X_final.append(X_normalized[i-seq_len:i])
		# 目标：当前时间点的目标值
		y_final.append(y_target_valid[i])

	X_final = np.array(X_final)
	y_final = np.array(y_final, dtype=np.int64)  # 类别标签，使用int64

	# 获取对应的日期信息
	# 需要考虑特征工程和标准化的偏移
	feature_start_idx = len(df) - len(features)  # 特征工程损失的索引
	norm_start_idx = feature_start_idx + valid_start  # 标准化损失的索引
	seq_start_idx = norm_start_idx + seq_len  # 序列创建损失的索引
	print(f"日期索引起始点: {seq_start_idx}")
	print(f"调试信息:")
	print(f"  原始数据长度: {len(df)}")
	print(f"  特征工程后长度: {len(features)}")
	print(f"  特征损失: {feature_start_idx}")
	print(f"  标准化起始: {valid_start}")
	print(f"  序列长度: {seq_len}")
	print(f"  最终样本数: {len(y_final)}")

	# 提取对应的日期
	dates_final = df['datetime'].iloc[seq_start_idx:seq_start_idx + len(y_final)].tolist()

	print(f"最终数据形状:")
	print(f"  X: {X_final.shape}")
	print(f"  y: {y_final.shape}")
	print(f"  特征维度: {X_final.shape[-1]}")
	print(f"  总共跳过了 {valid_start + seq_len} 个边界样本")
	print(f"  数据利用率: {len(y_final)}/{len(features)} = {len(y_final)/len(features)*100:.1f}%")

	return X_final, y_final, X_final.shape[-1], dates_final


def main():
	# 检测GPU是否可用
	device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
	print(f"使用设备: {device}")
	if torch.cuda.is_available():
		print(f"GPU名称: {torch.cuda.get_device_name(0)}")
		print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
	else:
		print("未检测到GPU，使用CPU进行计算")

	# 数据预处理
	model_path = 'model_trm.pth'
	X, y, input_dim, dates = prepare_stock_data('./big_data/AAPL_US_5min.csv', seq_len=200)
	# 转换为PyTorch张量并移动到设备
	X_tensor = torch.FloatTensor(X).to(device)
	y_tensor = torch.LongTensor(y).to(device)  # 类别标签，使用LongTensor
	print(f"模型输入维度: {input_dim}")
	print(f"数据张量形状: X={X_tensor.shape}, y={y_tensor.shape}")
	print(f"数据已移动到: {X_tensor.device}")
	# 分割数据为 训练集, 验证集, 测试集
	X_train, X_val, X_test, y_train, y_val, y_test, dates_train, dates_val, dates_test = split_data(X_tensor, y_tensor, dates)

	# 创建TensorDataset和DataLoader
	from torch.utils.data import TensorDataset, DataLoader

	train_dataset = TensorDataset(X_train, y_train)
	val_dataset = TensorDataset(X_val, y_val)
	test_dataset = TensorDataset(X_test, y_test)

	batch_size = 160

	train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)  # 恢复普通随机采样
	val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
	test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

	print(f"数据分割完成:")
	print(f"  训练集: {len(X_train)} 样本")
	print(f"  验证集: {len(X_val)} 样本")
	print(f"  测试集: {len(X_test)} 样本")
	print(f"  批次大小: {batch_size}")

	# 数据诊断
	print(f"\n数据诊断:")
	print(f"  X_train 范围: min={X_train.min():.4f}, max={X_train.max():.4f}, mean={X_train.mean():.4f}")
	print(f"  y_train 分布: {torch.bincount(y_train)}")
	print(f"  X_train 前5个样本的第一个时间步:")
	for i in range(min(5, len(X_train))):
		print(f"    样本{i}: {X_train[i, 0, :5].cpu().numpy()}")  # 显示前5个特征

	# 初始化模型并移动到设备
	model = TradeTransformer(
		input_dim=input_dim,
		pos_dim=16,
		model_dim=64,
		nhead=4,
		num_layers=2,
		num_classes=3,
		max_seq_len=200
	).to(device)
	# model = LSTM(
	# 	input_dim=input_dim,
	# 	hidden_dim=64,
	# 	num_layers=2,  # 减少层数避免梯度消失
	# 	output_dim=3,
	# 	device=device
	# ).to(device)
	print(f"模型初始化完成，参数数量: {sum(p.numel() for p in model.parameters())}")
	print(f"模型已移动到: {next(model.parameters()).device}")

	# 创建 损失函数 和 优化器
	# 计算类别权重来平衡不均衡的数据
	class_counts = torch.bincount(y_train)
	total_samples = len(y_train)
	class_weights = total_samples / (len(class_counts) * class_counts.float())

	# 温和增强涨跌类别的权重
	class_weights[0] *= 1.2  # 跌类权重增强20%
	class_weights[2] *= 1.2  # 涨类权重增强20%
	print(f"调整后类别权重: {class_weights}")

	criterion = nn.CrossEntropyLoss(weight=class_weights)
	optimizer = torch.optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)  # 降低学习率
	scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
		optimizer,
		mode='min',
		factor=0.7,  # 更温和的学习率衰减
		patience=8,  # 增加耐心
		min_lr=1e-6  # 设置最小学习率
	)
	# 开始训练
	num_epochs = 30
	best_val_accuracy = 0.0
	best_epoch = 0
	patience = 10
	trigger_times = 0
	start_time = time.time()
	if not os.path.exists(model_path):
		print('从零训练')
	else:
		print('加载模型')
		model.load_state_dict(torch.load(model_path, weights_only=True, map_location=device))
	for epoch in range(num_epochs):
		model.train()
		train_loss = 0.0
		for batch_x, batch_y in train_loader:
			batch_x, batch_y = batch_x.to(device), batch_y.to(device)  # 确保数据在正确设备上
			logits_train_pred = model(batch_x)  # [batch_size, 3]
			loss = criterion(logits_train_pred, batch_y)
			optimizer.zero_grad()
			loss.backward()

			# 梯度监控
			total_norm = 0
			for p in model.parameters():
				if p.grad is not None:
					param_norm = p.grad.data.norm(2)
					total_norm += param_norm.item() ** 2
			total_norm = total_norm ** (1. / 2)

			# 梯度裁剪防止梯度爆炸
			torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0)
			optimizer.step()
			train_loss += loss.item() * batch_x.size(0)
		train_loss = train_loss / len(train_dataset)
		# 验证阶段
		val_loss = 0.0
		correct_predictions = 0
		total_predictions = 0
		model.eval()
		with torch.no_grad():
			for batch_x, batch_y in val_loader:
				batch_x, batch_y = batch_x.to(device), batch_y.to(device)  # 确保数据在正确设备上
				logits_val_pred = model(batch_x)
				loss = criterion(logits_val_pred, batch_y)
				val_loss += loss.item() * batch_x.size(0)
				# 计算准确率
				pred_classes = torch.argmax(logits_val_pred, dim=1)
				correct_predictions += (pred_classes == batch_y).sum().item()
				total_predictions += batch_y.size(0)
		val_loss = val_loss / len(val_dataset)
		val_accuracy = correct_predictions / total_predictions * 100
		# 自动调整学习率
		scheduler.step(val_loss)
		cur_lr = optimizer.param_groups[0]['lr']
		print(f"Epoch: {epoch}, Train_Loss: {train_loss:.6f}, Val_Loss: {val_loss:.6f}, Val_Acc: {val_accuracy:.6f}, Lr: {cur_lr:.6f}, Grad_Norm: {total_norm:.4f}")

		# 保存模型
		if val_accuracy > best_val_accuracy:
			best_val_accuracy = val_accuracy
			best_epoch = epoch
			trigger_times = 0
			model_path = f'model_trm_{best_val_accuracy:.1f}.pth'
			torch.save(model.state_dict(), model_path)
		else:
			trigger_times += 1
			if trigger_times >= patience:
				print('Early Stoped')
				break
	training_time = time.time()-start_time
	print(f"Training time: {training_time}, Best epoch: {best_epoch}")
	# 测试模型
	model.load_state_dict(torch.load(model_path, weights_only=True, map_location=device))
	model.eval()
	
	# 计算测试集预测准确率和详细统计信息
	with torch.no_grad():
		# 对测试集进行预测
		test_logits = []
		test_predictions = []
		test_targets = []
		# 分批处理测试集
		debug = True
		for batch_x, batch_y in test_loader:
			batch_x, batch_y = batch_x.to(device), batch_y.to(device)
			logits_test_pred = model(batch_x)  # 输出 logits [batch_size, 3]
			test_logits.append(logits_test_pred)  # [batch_size, 3]
			test_targets.append(batch_y)  # [batch_size]

			batch_pred = torch.softmax(logits_test_pred, dim=-1)  # 转换为概率 [batch_size, 3]
			test_predictions.append(batch_pred)  # 保存概率用于分析
			if debug:
				debug = False
				print(f'batch_pred shape: {batch_pred.shape}, values: {batch_pred[:5]}')
				print(f'batch_y shape: {batch_y.shape}, values: {batch_y[:5]}')

		# 合并所有测试集预测结果
		test_logits = torch.cat(test_logits, dim=0)        # [test_samples, 3] - 原始 logits
		test_targets = torch.cat(test_targets, dim=0)          # [test_samples] - 目标类别

		test_predictions = torch.cat(test_predictions, dim=0)  # [test_samples, 3] - 预测概率分布
		print(f"test_predictions shape: {test_predictions.shape}")
		print(f"test_targets shape: {test_targets.shape}")

		# 计算交叉熵损失（使用原始 logits）
		test_loss = criterion(test_logits, test_targets).item()

		# 获取预测类别（概率最大的类别）
		pred_classes = torch.argmax(test_predictions, dim=1).cpu().numpy()
		true_classes = test_targets.cpu().numpy()

		# 计算分类准确率
		correct_predictions = (pred_classes == true_classes).sum()
		total_samples = len(test_targets)
		overall_accuracy = correct_predictions / total_samples * 100

		# 获取预测概率的最大值（用于显示置信度）
		pred_probs = torch.max(test_predictions, dim=1)[0].cpu().numpy()

		# 统计信息
		print(f"{'='*60}")
		print(f"测试集结果:")
		print(f"  交叉熵损失: {test_loss:.6f}")
		print(f"  分类准确率: {overall_accuracy:.2f}%")
		print(f"  预测置信度范围: {pred_probs.min():.4f} - {pred_probs.max():.4f}")
		pred_dist = np.bincount(pred_classes, minlength=3)
		true_dist = np.bincount(true_classes, minlength=3)
		print(f"预测类别分布 (0:跌, 1:平, 2:涨): {pred_dist}")
		print(f"真实类别分布 (0:跌, 1:平, 2:涨): {true_dist}")

		# 计算每个类别的准确率
		print(f"\n各类别准确率:")
		class_names = ['跌', '平', '涨']
		for i in range(3):
			# 找到真实标签为类别i的所有样本
			mask = (true_classes == i)
			if np.sum(mask) > 0:
				# 计算在这些样本中，预测正确的比例
				class_acc = np.mean(pred_classes[mask] == i) * 100
				print(f"  类别{i} ({class_names[i]}): {class_acc:.2f}% ({np.sum(mask)}个样本)")
			else:
				print(f"  类别{i} ({class_names[i]}): 无样本")
		print(f"{'='*60}")

	

if __name__ == "__main__":
	main()